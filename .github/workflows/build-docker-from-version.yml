name: Build Docker Image from Version

on:
  workflow_call:
    inputs:
      version:
        required: true
        type: string
      branch:
        required: true
        type: string
      tag_latest:
        required: false
        type: string
        default: 'false'

  workflow_dispatch:
    inputs:
      version:
        description: 'Version to build and push'
        required: true
      branch:
        description: 'Branch to checkout'
        required: true
      tag_latest:
        description: 'Tag this image as latest too?'
        required: false
        default: 'false'

env:
  VERSION_FILE: version.json
  DOCKER_IMAGE_NAME: ${{ vars.DOCKER_IMAGE_NAME }}

jobs:
  docker-build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        ref: ${{ inputs.branch }}

    - name: Docker login
      run: echo "${{ secrets.REGISTRY_TOKEN }}" | docker login ${{ secrets.REGISTRY_URL }} -u ${{ secrets.REGISTRY_USERNAME }} --password-stdin

    - name: Docker build
      run: |
        docker build -t ${{ secrets.REGISTRY_URL }}/${{ env.DOCKER_IMAGE_NAME }}:${{ inputs.version }} . -f Dockerfile.production
        
        
    - name: Add 'latest' tag
      if: ${{ inputs.tag_latest == 'true' }}
      run: |
        docker tag ${{ secrets.REGISTRY_URL }}/${{ env.DOCKER_IMAGE_NAME }}:${{ inputs.version }} ${{ secrets.REGISTRY_URL }}/${{ env.DOCKER_IMAGE_NAME }}:latest        

    - name: Push Docker image
      run: |
        docker push ${{ secrets.REGISTRY_URL }}/${{ env.DOCKER_IMAGE_NAME }} --all-tags

    - name: Notify Success
      if: success()
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: ${{ secrets.SMTP_SERVER }}
        server_port: 587
        username: ${{ secrets.SMTP_USERNAME }}
        password: ${{ secrets.SMTP_PASSWORD }}
        subject: "✅ Docker Build Success - ${{ inputs.version }}"
        to: ${{ vars.EMAIL_RECIPIENTS }}
        from: ${{ vars.EMAIL_FROM }}
        body: |
          ✅ Docker image built and pushed successfully:
          ${{ env.DOCKER_IMAGE_NAME }}:${{ inputs.version }}

    - name: Notify Failure
      if: failure()
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: ${{ secrets.SMTP_SERVER }}
        server_port: 587
        username: ${{ secrets.SMTP_USERNAME }}
        password: ${{ secrets.SMTP_PASSWORD }}
        subject: "❌ Docker Build Failed - ${{ inputs.version }}"
        to: ${{ vars.EMAIL_RECIPIENTS }}
        from: ${{ vars.EMAIL_FROM }}
        body: |
          ❌ Docker build or push failed for version ${{ inputs.version }}.
