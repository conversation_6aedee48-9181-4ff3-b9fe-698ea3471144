name: Manual Version Bump

on:
  workflow_dispatch:
    inputs:
      bump_type:
        description: 'Version bump type (only for main)'
        required: false
        default: 'patch'
        type: choice
        options:
          - major
          - minor
          - patch

env:
  VERSION_FILE: version.json

jobs:
  bump-version:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repo
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
        ref: ${{ github.ref_name }}

    - name: Set up Git
      run: |
        git config user.name "github-actions"
        git config user.email "<EMAIL>"

    - name: Install jq
      run: sudo apt-get install -y jq

    - name: Calculate new version
      id: versioning
      run: |
        BRANCH="${{ github.ref_name }}"
        VERSION=$(jq -r .version "$VERSION_FILE")
        IFS='.' read -r MAJOR MINOR PATCH <<< "$VERSION"
        if [ "$BRANCH" = "main" ]; then
          TYPE="${{ github.event.inputs.bump_type }}"
          if [ "$TYPE" = "major" ]; then
            MAJOR=$((MAJOR + 1))
            MINOR=0
            PATCH=0
          elif [ "$TYPE" = "minor" ]; then
            MINOR=$((MINOR + 1))
            PATCH=0
          else
            PATCH=$((PATCH + 1))
          fi
          NEW_VERSION="${MAJOR}.${MINOR}.${PATCH}"
        else
          DATETIME=$(date +'%Y%m%d%H%M')
          NEW_VERSION="${MAJOR}.${MINOR}.${PATCH}.${DATETIME}"
        fi
        echo "Bumped version: $NEW_VERSION"
        echo "{\"version\": \"$NEW_VERSION\"}" > "$VERSION_FILE"
        echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT

    - name: Commit and push
      if: github.ref_name == 'main'
      run: |
        git add $VERSION_FILE
        git commit -m "chore: bump version to ${{ steps.versioning.outputs.new_version }}"
        git push origin ${{ github.ref_name }}

    - name: Create release branch (main only)
      if: github.ref_name == 'main'
      run: |
        git checkout -b releases/${{ steps.versioning.outputs.new_version }}
        git push origin releases/${{ steps.versioning.outputs.new_version }}
        
    - name: Trigger Docker Build Workflow
      uses: actions/github-script@v7
      with:
        script: |
          await github.rest.actions.createWorkflowDispatch({
            owner: context.repo.owner,
            repo: context.repo.repo,
            workflow_id: 'build-docker-from-version.yml',
            ref: '${{ github.ref_name }}',
            inputs: {
              version: '${{ steps.versioning.outputs.new_version }}',
              branch: '${{ github.ref_name }}',
              tag_latest: '${{ github.ref_name == 'main' }}'
            }
          });
