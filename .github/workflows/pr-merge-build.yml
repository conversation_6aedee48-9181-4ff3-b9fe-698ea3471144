name: PR Build & Docker Test Image

on:
  pull_request:
    paths-ignore:
      - '.github/**'
      
concurrency:
  group: pr-${{ github.event.pull_request.number }}
  cancel-in-progress: true

env:
  VERSION_FILE: version.json
  DOCKER_IMAGE_NAME: ${{ vars.DOCKER_IMAGE_NAME }}

jobs:
  pr-build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Install jq
      run: sudo apt-get install -y jq

    - name: Read version and append -test
      id: versioning
      run: |
        CURRENT_VERSION=$(jq -r .version $VERSION_FILE)
        IFS='.' read -r MAJOR MINOR PATCH <<< "$CURRENT_VERSION"
        PATCH=$((PATCH + 1))
        BUILD_ID=${GITHUB_RUN_ID}
        NEW_VERSION="${MAJOR}.${MINOR}.${PATCH}-test-${BUILD_ID}"
        echo "TEST_VERSION=$NEW_VERSION" >> $GITHUB_ENV
        echo "Using test version: $NEW_VERSION"

    - name: Docker login
      run: echo "${{ secrets.REGISTRY_TOKEN }}" | docker login ${{ secrets.REGISTRY_URL }} -u ${{ secrets.REGISTRY_USERNAME }} --password-stdin

    - name: Docker build
      run: |
        docker build -t ${{ secrets.REGISTRY_URL }}/${{ env.DOCKER_IMAGE_NAME }}:${{ env.TEST_VERSION }} . -f Dockerfile.production

    - name: Docker push
      run: |
        docker push ${{ secrets.REGISTRY_URL }}/${{ env.DOCKER_IMAGE_NAME }} --all-tags

    - name: Notify Success
      if: success()
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: ${{ secrets.SMTP_SERVER }}
        server_port: 587
        username: ${{ secrets.SMTP_USERNAME }}
        password: ${{ secrets.SMTP_PASSWORD }}
        subject: "✅ PR Build Success - ${{ env.TEST_VERSION }}"
        to: ${{ vars.EMAIL_RECIPIENTS }}
        from: ${{ vars.EMAIL_FROM }}
        body: |
          ✅ Docker image built and pushed successfully.
          Image: ${{ env.DOCKER_IMAGE_NAME }}:${{ env.TEST_VERSION }}

    - name: Notify Failure
      if: failure()
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: ${{ secrets.SMTP_SERVER }}
        server_port: 587
        username: ${{ secrets.SMTP_USERNAME }}
        password: ${{ secrets.SMTP_PASSWORD }}
        subject: "❌ PR Build Failed"
        to: ${{ vars.EMAIL_RECIPIENTS }}
        from: ${{ vars.EMAIL_FROM }}
        body: |
          ❌ Docker image build or push failed for PR.
