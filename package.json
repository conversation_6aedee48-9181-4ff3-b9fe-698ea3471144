{"name": "fwc-salus", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.11.2", "@strapi/plugin-users-permissions": "5.11.2", "@strapi/strapi": "5.11.2", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "stripe": "^18.1.0", "styled-components": "^6.0.0", "xero-node": "^11.1.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}