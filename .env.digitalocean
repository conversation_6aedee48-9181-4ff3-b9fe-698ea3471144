
# Server
HOST=0.0.0.0
PORT=1337

# Secrets
APP_KEYS=84WyRkMpSKev/GoXdQfldw==,HT97uTlRDI8xl8G7VDwa6Q==,c9pWxwSX+TTlINCY89IPOA==,zJvg5+KCKKD2SIMt00vI8w==
API_TOKEN_SALT=5XWFgX8g4CClx97jzAJWJw==
ADMIN_JWT_SECRET=cf2GZeqhoM0Rzo6wVvAwiA==
TRANSFER_TOKEN_SALT=JOWI80SzNPZKuA7HyGdG3Q==

# Database
DATABASE_CLIENT=postgres
DATABASE_HOST=db-postgresql-salus-do-user-19879194-0.f.db.ondigitalocean.com
DATABASE_PORT=25060
DATABASE_NAME=defaultdb
DATABASE_USERNAME=doadmin
DATABASE_PASSWORD=AVNS_HxCf8IvZBfTd3uaqaLz
DATABASE_URL=postgres://doadmin:<EMAIL>:25060/defaultdb
# SSL Settings (for DigitalOcean)
DATABASE_SSL=true
DATABASE_SSL_CA=/etc/ssl/digitalocean-ca.crt
DATABASE_SSL_REJECT_UNAUTHORIZED=false
# Connection Pooling
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_CONNECTION_TIMEOUT=60000
# Schema (Default: public)
DATABASE_SCHEMA=public