# path: ./Dockerfile.prod

FROM node:22 AS pre-build
# Installing libvips-dev for sharp Compatibility
# RUN apk update && apk add build-base gcc autoconf automake zlib-dev libpng-dev vips-dev && rm -rf /var/cache/apk/* > /dev/null 2>&1
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
WORKDIR /opt/
COPY ./package.json ./
ENV PATH=/opt/node_modules/.bin:$PATH
RUN npm install --fetch-timeout=600000

FROM pre-build AS build
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV PATH=/opt/node_modules/.bin:$PATH
WORKDIR /opt/app
COPY ./ .
RUN npm run build

FROM node:22 AS final
# RUN apk add vips-dev
# RUN rm -rf /var/cache/apk/*
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
WORKDIR /opt/app
COPY --from=build /opt/node_modules ./node_modules
ENV PATH=/opt/node_modules/.bin:$PATH
COPY --from=build /opt/app ./
ENV PORT=1337
EXPOSE 1337
CMD ["npm", "start"]
