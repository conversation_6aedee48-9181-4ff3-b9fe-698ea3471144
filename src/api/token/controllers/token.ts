/**
 * token controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::token.token', ({ strapi }) => ({
  // Keep the default core controller methods
  
  // Add custom verify method
  async verify(ctx) {
    try {
      // Get token from request
      const { token } = ctx.request.body;
      
      if (!token) {
        return ctx.badRequest('Token is required');
      }
      
      // Use Strapi's JWT service to verify and decode
      const { id, iat, exp } = await strapi.plugins['users-permissions'].services.jwt.verify(token);
      
      // Check if token is expired
      const currentTimestamp = Math.floor(Date.now() / 1000);
      if (exp <= currentTimestamp) {
        return {
          expired: true,
          message: 'User is expired'
        };
      }
      
      // Get user details if needed (optional)
      const user = await strapi.entityService.findOne('plugin::users-permissions.user', id, {
        fields: ['id', 'username', 'email']
      });
      
      // Return successful response
      return {
        expired: false,
        userId: id,
        user: user || { id }
      };
    } catch (err) {
      // JWT verification failed
      return {
        expired: true,
        message: 'Invalid token'
      };
    }
  }
}));