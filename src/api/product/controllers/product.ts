/**
 * product controller
 */
import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';

export default factories.createCoreController('api::product.product', ({ strapi }) => ({
  // Keep the default controller actions

  // Add custom controller action to update a product by ID
  async updateProductById(ctx: Context, next: Next) {
    const { id } = ctx.params;

    if (!id) {
      return ctx.badRequest('Product ID is required in the URL parameters.');
    }

    const { data } = ctx.request.body || {};

    if (!data || typeof data !== 'object') {
      return ctx.badRequest('Data object is required in the request body.');
    }

    try {
      // Check if product exists
      const existingProduct = await strapi.entityService.findOne('api::product.product', id);

      if (!existingProduct) {
        return ctx.notFound('Product not found with the provided ID.');
      }

      // Update the product, now including image_details
      const updatedProduct = await strapi.entityService.update('api::product.product', id, {
        data: {
          Name: data.Name,
          unit_price: data.unit_price,
          product_description: data.product_description,
          supplier_fk: data.supplier_fk,
          category_fk: data.category_fk,
          //discount_fk: data.discount_fk,
          discount_details: data.discount_details,
          tags: data.tags,
          bulk_pricing: data.bulk_pricing,
          product_status: data.product_status,
          image_details: data.image_details, // <-- Added this line
        },
      });

      return ctx.send({
        data: updatedProduct,
        message: 'Product updated successfully'
      });
    } catch (err: any) {
      console.error('Error updating product:', err);
      return ctx.internalServerError(`An error occurred: ${err.message}`);
    }
  },

  // Add custom controller action for filtering products by category_id and/or supplier_fk
  async filterProducts(ctx: Context) {
    try {
      const { category_id, supplier_fk } = ctx.query;

      // Build filters based on provided query parameters
      const filters: any = {};

      if (category_id) {
        if (category_id === 'null') {
          filters.category_fk = { $null: true };
        } else {
          filters.category_fk = parseInt(category_id as string);
        }
      }

      if (supplier_fk) {
        if (supplier_fk === 'null') {
          filters.supplier_fk = { $null: true };
        } else {
          filters.supplier_fk = parseInt(supplier_fk as string);
        }
      }

      // Get pagination parameters or use defaults
      const page = parseInt(ctx.query.page as string) || 1;
      const pageSize = parseInt(ctx.query.pageSize as string) || 10;

      // Get sorting parameter or use default
      let sortValue: any = [{ createdAt: 'desc' }]; // Default sorting

      if (ctx.query.sort) {
        const sortParam = ctx.query.sort as string;
        const [field, order] = sortParam.split(':');

        if (field && order) {
          sortValue = [{ [field]: order.toLowerCase() }];
        }
      }

      // Query with filters
      const products = await strapi.entityService.findMany('api::product.product', {
        filters,
        sort: sortValue,
        populate: ['category_fk', 'supplier_fk'], // Populate relations
        pagination: {
          page,
          pageSize
        }
      });

      // Add sale and new flags to each product
      const currentDate = new Date();
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

      const productsWithFlags = products.map(product => {
        // Check if product is on sale (has discount_details)
        const onSale = product.discount_details !== null && product.discount_details !== undefined;

        // Check if product is new (createdAt within last month)
        const createDate = new Date(product.createdAt);
        const isNew = createDate >= oneMonthAgo;

        return {
          ...product,
          on_sale: onSale,
          is_new: isNew
        };
      });

      // Count total matching products
      const count = await strapi.db.query('api::product.product').count({
        where: filters
      });

      // Prepare response
      return {
        data: productsWithFlags,
        meta: {
          pagination: {
            page,
            pageSize,
            pageCount: Math.ceil(count / pageSize),
            total: count
          }
        }
      };
    } catch (err: any) {
      console.error('Error filtering products:', err);
      return ctx.internalServerError(`An error occurred: ${err.message}`);
    }
  },


  async getProductsWithPromos(ctx) {
    try {
      // Parse pagination parameters
      const pageParam = ctx.query.page ? parseInt(String(ctx.query.page), 10) : 1;
      const pageSizeParam = ctx.query.pageSize ? parseInt(String(ctx.query.pageSize), 10) : 10;
      const page = isNaN(pageParam) ? 1 : pageParam;
      const pageSize = isNaN(pageSizeParam) ? 10 : pageSizeParam;
      
      // First, let's check the promo schema to find the correct product relation field name
      const promoSchema = strapi.getModel('api::promo.promo');
      console.log('Promo attributes:', Object.keys(promoSchema.attributes));
      
      // Find product relation field name
      const productRelationField = Object.entries(promoSchema.attributes)
        .find(([key, attr]: [string, any]) => 
          attr.type === 'relation' && attr.target === 'api::product.product'
        )?.[0];
      
      console.log(`Found product relation field in promo: ${productRelationField || 'none'}`);
      
      if (!productRelationField) {
        return ctx.badRequest('No product relation field found in promo schema');
      }
      
      // Create populate object with the correct field name
      const populate: any = {};
      populate[productRelationField] = true;
      
      // Get all promos with their products, sorted by promo creation date
      const promosResult = await strapi.entityService.findMany('api::promo.promo', {
        sort: { createdAt: 'desc' },
        populate
      });
      
      // Type assertion to deal with TypeScript
      const promos = promosResult as any[];
      
      console.log(`Found ${promos.length} promos`);
      
      // Create a map to store the latest promo date for each product
      const productMap = new Map();
      
      // Process promos and extract unique products with their latest promo date
      promos.forEach(promo => {
        // Dynamically access the product relation field
        const products = promo[productRelationField];
        
        if (products && Array.isArray(products)) {
          products.forEach(product => {
            // Only add the product if it's not in our map yet or if this promo is newer
            if (!productMap.has(product.id) || 
                new Date(promo.createdAt) > new Date(productMap.get(product.id).promoDate)) {
              
              productMap.set(product.id, {
                product: product,
                promoDate: promo.createdAt,
                promoId: promo.id,
                promoTitle: promo.Title
              });
            }
          });
        }
      });
      
      console.log(`Extracted ${productMap.size} unique products with promos`);
      
      // Convert the map to an array and sort by promo date
      const uniqueProducts = Array.from(productMap.values())
        .sort((a, b) => new Date(b.promoDate).getTime() - new Date(a.promoDate).getTime());
      
      // Apply pagination
      const totalCount = uniqueProducts.length;
      const paginatedProducts = uniqueProducts.slice((page-1) * pageSize, page * pageSize);
      
      // Fetch complete product data with all relations
      const enhancedProducts = await Promise.all(
        paginatedProducts.map(async (item) => {
          const productId = item.product.id;
          const completeProduct = await strapi.entityService.findOne('api::product.product', productId, {
            populate: {
              supplier_fk: true,
              category_fk: true
            }
          });
          
          // Add the promo information to the product
          return {
            ...completeProduct,
            latest_promo: {
              id: item.promoId,
              title: item.promoTitle,
              date: item.promoDate
            }
          };
        })
      );
      
      // Return formatted response
      return {
        data: enhancedProducts,
        meta: {
          pagination: {
            page: page,
            pageSize: pageSize,
            pageCount: Math.ceil(totalCount / pageSize),
            total: totalCount
          }
        }
      };
    } catch (error) {
      console.error('Error fetching products with promos:', error);
      return ctx.internalServerError(`An error occurred: ${error.message}`);
    }
  }
}));