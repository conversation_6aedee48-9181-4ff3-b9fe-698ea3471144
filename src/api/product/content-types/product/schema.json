{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"Name": {"type": "string"}, "unit_price": {"type": "decimal"}, "product_description": {"type": "text"}, "bulk_pricing": {"type": "decimal"}, "product_status": {"type": "string"}, "supplier_fk": {"type": "relation", "relation": "oneToOne", "target": "api::supplier.supplier"}, "category_fk": {"type": "relation", "relation": "oneToOne", "target": "api::category.category"}, "tags": {"type": "text"}, "discount_details": {"type": "json"}, "image_details": {"type": "json"}, "available_quantity": {"type": "integer"}, "promo_fk": {"type": "relation", "relation": "oneToMany", "target": "api::promo.promo"}}}