export default {
  routes: [
    {
      method: 'PUT',
      path: '/products/:id/update',
      handler: 'product.updateProductById',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/products/filter',
      handler: 'product.filterProducts',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/products/with-promos',
      handler: 'product.getProductsWithPromos',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get products that have promotions, sorted by promo creation date',
      },
    },
  ],
};