// src/api/order-item/routes/custom-order-item.ts

export default {
    routes: [
      {
        method: 'GET',
        path: '/order-items/analytics/monthly-category',
        handler: 'order-item.getOrderValueByMonthAndCategory',
        config: {
          policies: [],
          middlewares: [],
          description: 'Get aggregated order values by month and category',
        },
      }
    ],
  };