import { factories } from '@strapi/strapi';
import { Context } from 'koa';

interface OrderValueByMonthAndCategory {
  year: number;
  month: number;
  categoryId: number;
  categoryName: string;
  totalValue: number;
  count: number;
}

export default factories.createCoreController('api::order-item.order-item', ({ strapi }) => ({
  // Keep the default controller actions
  ...factories.createCoreController('api::order-item.order-item'),
  
  // Add our custom controller method
  async getOrderValueByMonthAndCategory(ctx: Context) {
    try {
      // Parse query parameters
      const { startDate, endDate } = ctx.query;
      
      // First, get all the order items (with minimal data)
      const orderItemsQuery: any = {};
      if (startDate && endDate) {
        orderItemsQuery.filters = {
          createdAt: {
            $gte: new Date(String(startDate)),
            $lte: new Date(String(endDate))
          }
        };
      }
      
      const orderItems = await strapi.entityService.findMany('api::order-item.order-item', orderItemsQuery);
      
      // Now for each order item, get product details with category
      const enhancedOrderItems = await Promise.all(orderItems.map(async (item: any) => {
        // For each order item, get its product with category
        if (!item.product_name) {
          return { ...item, category: null };
        }
        
        // Find the product by name
        const productResult = await strapi.db.query('api::product.product').findOne({
          where: { Name: item.product_name },
          populate: ['category_fk']
        });
        
        let category = null;
        if (productResult && productResult.category_fk) {
          category = {
            id: productResult.category_fk.id,
            name: productResult.category_fk.Name
          };
        }
        
        return { ...item, category };
      }));
      
      // Log the first enhanced item to verify
      if (enhancedOrderItems.length > 0) {
        console.log('Enhanced order item:', JSON.stringify(enhancedOrderItems[0], null, 2));
      }
      
      // Now process the results to group by month and category
      const monthCategoryMap: Record<string, Record<string, OrderValueByMonthAndCategory>> = {};
      
      for (const item of enhancedOrderItems) {
        const date = new Date(item.createdAt);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
        
        // Skip items without necessary data
        if (!item.price || !item.quantity) {
          continue;
        }
        
        // Determine category information
        let categoryId = 0; // Default for uncategorized
        let categoryName = "Uncategorized"; // Default category name
        
        if (item.category) {
          categoryId = item.category.id;
          categoryName = item.category.name;
        }
        
        const categoryKey = `${categoryId}`;
        
        // Calculate this item's value
        const itemPrice = Number(item.price) || 0;
        const itemQuantity = Number(item.quantity) || 0;
        const itemValue = itemPrice * itemQuantity;
        
        // Initialize month if not exists
        if (!monthCategoryMap[monthKey]) {
          monthCategoryMap[monthKey] = {};
        }
        
        // Initialize category if not exists
        if (!monthCategoryMap[monthKey][categoryKey]) {
          monthCategoryMap[monthKey][categoryKey] = {
            year,
            month,
            categoryId,
            categoryName,
            totalValue: 0,
            count: 0
          };
        }
        
        // Update the aggregation
        monthCategoryMap[monthKey][categoryKey].totalValue += itemValue;
        monthCategoryMap[monthKey][categoryKey].count += 1;
      }
      
      // Format the result for response
      const result: Record<string, OrderValueByMonthAndCategory[]> = {};
      Object.entries(monthCategoryMap).forEach(([monthKey, categories]) => {
        result[monthKey] = Object.values(categories);
      });
      
      return {
        data: result,
        meta: {
          dateRange: startDate && endDate ? { startDate, endDate } : 'all time',
          totalItems: orderItems.length
        }
      };
      
    } catch (error: any) {
      console.error('Error aggregating order values:', error);
      return ctx.internalServerError(`An error occurred: ${error.message}`);
    }
  }
}));