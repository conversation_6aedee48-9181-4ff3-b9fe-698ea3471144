{"kind": "collectionType", "collectionName": "order_items", "info": {"singularName": "order-item", "pluralName": "order-items", "displayName": "order-item"}, "options": {"draftAndPublish": true}, "attributes": {"price": {"type": "decimal"}, "discounted_price": {"type": "decimal"}, "quantity": {"type": "integer"}, "order_fk": {"type": "relation", "relation": "oneToOne", "target": "api::order.order"}, "shipping_cost": {"type": "decimal"}, "notes": {"type": "string"}, "size": {"type": "string"}, "supplier_fk": {"type": "relation", "relation": "oneToOne", "target": "api::supplier.supplier"}, "product_name": {"type": "string"}, "product_fk": {"type": "relation", "relation": "oneToOne", "target": "api::product.product"}}}