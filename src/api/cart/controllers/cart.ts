import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';

export default factories.createCoreController('api::cart.cart', ({ strapi }) => ({
  // Updated create method with more robust duplicate checking and explicit publication
  // async create(ctx) {
  //   const { data } = ctx.request.body;
    
  //   if (!data) {
  //     return ctx.badRequest('Missing request body data');
  //   }
    
  //   const { user_fk, product_fk } = data;
    
  //   // More robust check for existing cart entry
  //   if (user_fk !== undefined && product_fk !== undefined) {
  //     try {
  //       // Log the query parameters
  //       console.log(`Checking for existing cart with user_fk=${user_fk}, product_fk=${product_fk}`);
        
  //       // Use a more specific query structure for relations
  //       const existingCart = await strapi.db.query('api::cart.cart').findOne({
  //         where: {
  //           user_fk: user_fk,
  //           product_fk: product_fk
  //         }
  //       });
        
  //       console.log('Existing cart search result:', existingCart ? `Found ID: ${existingCart.id}` : 'None found');
        
  //       if (existingCart) {
  //         // Update the cart entry with proper publication
  //         const updated = await strapi.entityService.update('api::cart.cart', existingCart.id, {
  //           data: data,
  //           // Explicitly set to published status
  //           publicationState: 'live',
  //           // Force the publishedAt to be set if not already
  //           ...(existingCart.publishedAt ? {} : { publishedAt: new Date() })
  //         });
          
  //         return {
  //           data: updated,
  //           meta: { action: 'updated' }
  //         };
  //       }
  //     } catch (err) {
  //       console.error('Error checking for existing cart:', err);
  //       // Continue to creation if the check fails
  //     }
  //   }
    
  //   // If not found, proceed with normal create and ensure it's published
  //   console.log('No existing cart found, creating new entry');
    
  //   // Override the standard create to ensure publication
  //   try {
  //     // Create with explicit publication
  //     const created = await strapi.entityService.create('api::cart.cart', {
  //       data: {
  //         ...data,
  //         publishedAt: new Date() // Ensure publishedAt is set for new entries
  //       }
  //     });
      
  //     return {
  //       data: created
  //     };
  //   } catch (err) {
  //     console.error('Error creating cart entry:', err);
  //     return ctx.internalServerError(`Error creating cart entry: ${err.message}`);
  //   }
  // },
  
  // Method to find cart by user and product
  async findByUserAndProduct(ctx) {
    try {
      // Get user_fk and product_fk from URL parameters
      const { user_fk, product_fk } = ctx.params;
      
      // Input validation
      if (!user_fk || !product_fk) {
        return ctx.badRequest('Missing required parameters: user_fk and product_fk');
      }
      
      // Convert string parameters to numbers
      const userFk = parseInt(user_fk, 10);
      const productFk = parseInt(product_fk, 10);
      
      if (isNaN(userFk) || isNaN(productFk)) {
        return ctx.badRequest('Invalid parameters: user_fk and product_fk must be valid numbers');
      }
      
      // Find cart entry
      const cart = await strapi.db.query('api::cart.cart').findOne({
        where: {
          user_fk: userFk,
          product_fk: productFk,
          cart_status: 'pending'  // Only fetch pending items
        },
        populate: ['user_fk', 'product_fk']  // Include related data
      });
      
      if (!cart) {
        return ctx.notFound('No pending cart entry found for this user and product combination');
      }
      
      // Return the result
      return {
        data: cart
      };
    } catch (err) {
      console.error('Error fetching pending cart by user and product:', err);
      return ctx.internalServerError(`An error occurred: ${err.message}`);
    }
  }
}));