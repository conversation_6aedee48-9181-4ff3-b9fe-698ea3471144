{"kind": "collectionType", "collectionName": "carts", "info": {"singularName": "cart", "pluralName": "carts", "displayName": "cart", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"Quantity": {"type": "integer"}, "product_fk": {"type": "relation", "relation": "oneToOne", "target": "api::product.product"}, "user_fk": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "size": {"type": "integer"}, "cart_status": {"type": "string"}, "supplier_fk": {"type": "relation", "relation": "oneToOne", "target": "api::supplier.supplier"}, "notes": {"type": "string"}}}