// src/api/xero-token/services/xero-token.ts
import { factories } from '@strapi/strapi';
import { XeroClient } from 'xero-node';
import type { TokenSet } from 'openid-client';

export default factories.createCoreService('api::xero-token.xero-token', ({ strapi }) => ({
  // Get the XeroClient instance using environment variables
  getXeroClient() {
    return new XeroClient({
      clientId: process.env.XERO_CLIENT_ID || 'C3F2B637BC774020826AD36C3FF31C23',
      clientSecret: process.env.XERO_CLIENT_SECRET || 'rrZeJfubEu4Q5EHKaPGWWn6BjYeXpZAsq10sjUlxt2BY5Ufb',
      redirectUris: [process.env.XERO_REDIRECT_URI || 'https://salusstaging.findwhyclients.com/api/xero/callback'],
      scopes: [ 'accounting.transactions', 'accounting.settings', 'offline_access'],
    });
  },
  
  // Get authenticated client with valid tokens
  // In your xero-token.ts service file, update getAuthenticatedClient method
  // In getAuthenticatedClient method:
  async getAuthenticatedClient() {
    try {
      // Define TokenResponse interface
      interface TokenResponse {
        access_token: string;
        refresh_token: string;
        expires_in: number;
        token_type: string;
        scope?: string;
      }
      
      console.log("Starting Xero authentication process");
      
      // Find the most recent token
      const tokens = await strapi.entityService.findMany('api::xero-token.xero-token', {
        sort: { expiresAt: 'desc' },
        limit: 1
      });
  
      if (!tokens || tokens.length === 0) {
        console.log("No Xero tokens found in database");
        throw new Error('No Xero token available');
      }
  
      const token = tokens[0];
      console.log(`Found token ID: ${token.id}, expires: ${token.expiresAt}`);
      
      // Log credentials for debugging (partial)
      const clientId = process.env.XERO_CLIENT_ID;
      const clientSecret = process.env.XERO_CLIENT_SECRET;
      
      console.log("===== XERO CREDENTIALS CHECK =====");
      console.log(`Client ID: ${clientId?.substring(0, 4)}...${clientId?.substring(clientId.length - 4)}`);
      console.log(`Client Secret: ${clientSecret?.substring(0, 4)}...${clientSecret?.substring(clientSecret.length - 4)}`);
      console.log(`Token ID from DB: ${token.id}`);
      console.log(`Token Access Length: ${token.acessToken?.length || 0}`);
      console.log(`Token Refresh Length: ${token.refreshToken?.length || 0}`);
      console.log("=================================");
      
      // Initialize the client
      const xero = new XeroClient({
        clientId: clientId,
        clientSecret: clientSecret,
        redirectUris: [process.env.XERO_REDIRECT_URI],
        scopes: ['accounting.transactions', 'accounting.settings', 'offline_access'],
      });
      
      // Manual token refresh - proper OAuth2 refresh token flow
      console.log("Performing manual OAuth2 token refresh");
      
      try {
        const refreshTokenUrl = 'https://identity.xero.com/connect/token';
        
        // Create authorization header with Base64 encoded client_id:client_secret
        const authHeader = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
        
        // Perform token refresh request
        const response = await fetch(refreshTokenUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${authHeader}`
          },
          body: new URLSearchParams({
            grant_type: 'refresh_token',
            refresh_token: token.refreshToken
          }).toString()
        });
        
        // Handle response errors
        if (!response.ok) {
          const errorData = await response.text();
          console.error("Token refresh failed:", response.status, errorData);
          throw new Error(`Token refresh failed: ${response.status} - ${errorData}`);
        }
        
        // Parse the response with proper typing
        const newTokenData = await response.json() as TokenResponse;
        console.log("Token refresh successful, got new token data");
        
        // Update token in database with the correct field names
        console.log("Updating token in database...");
        await strapi.entityService.update('api::xero-token.xero-token', token.id, {
          data: {
            acessToken: newTokenData.access_token, // Note the field name matches your DB column
            refreshToken: newTokenData.refresh_token,
            expiresAt: new Date(Date.now() + newTokenData.expires_in * 1000)
          }
        });
        console.log("Token updated in database");
        
        // Set the token in the Xero client
        xero.setTokenSet({
          access_token: newTokenData.access_token,
          refresh_token: newTokenData.refresh_token,
          expires_at: Math.floor(Date.now() / 1000) + newTokenData.expires_in,
          token_type: newTokenData.token_type || "Bearer"
        });
        
        console.log("Updating tenants...");
        await xero.updateTenants();
        console.log(`Found ${xero.tenants?.length || 0} tenants`);
        
        return xero;
      } catch (error) {
        console.error("Error refreshing token:", error);
        throw new Error(`Failed to refresh Xero token: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in getAuthenticatedClient:", error);
      throw error;
    }
  },
  /**
   * Create an invoice in Xero from order data
   */
  async createInvoice(orderData) {
    try {
      // First check if we have order items
      if (!orderData.order_items || orderData.order_items.length === 0) {
        console.log("No order items found, cannot create invoice");
        return null;
      }
  
      // Get authenticated client
      const xero = await this.getAuthenticatedClient();
      await xero.updateTenants();
      const tenants = xero.tenants;
      
      if (!tenants || tenants.length === 0) {
        console.error("No Xero tenants available");
        throw new Error("No Xero tenants available");
      }
      
      const tenantId = tenants[0].tenantId;
      console.log(`Using Xero tenant: ${tenantId}`);
      
      // Create line items array from order items with proper GST settings
      const lineItems = orderData.order_items.map(item => {
        const amount = (item.discounted_price || item.price || 0);
        
        return {
          description: item.product_name || "Product",
          quantity: item.quantity || 1,
          unitAmount: amount.toString(),
          accountCode: "200", // Use your actual account code
          taxType: "OUTPUT", // Australian GST
          taxAmount: (amount * 0.1 * (item.quantity || 1)).toString() // 10% GST
        };
      });
      
      // Check if shipping cost exists and add as separate line item
      if (orderData.shipping_cost && parseFloat(orderData.shipping_cost) > 0) {
        const shippingAmount = parseFloat(orderData.shipping_cost);
        lineItems.push({
          description: "Shipping",
          quantity: 1,
          unitAmount: shippingAmount.toString(),
          accountCode: "425", // Different account code for shipping
          taxType: "OUTPUT", // Same tax type for shipping
          taxAmount: (shippingAmount * 0.1).toString() // 10% GST on shipping
        });
        console.log(`Added shipping cost: $${shippingAmount} to invoice`);
      }
      
      console.log(`Created ${lineItems.length} line items for invoice with GST`);
      
      // Build the complete invoice object with required fields
      const invoiceData = {
        invoices: [{
          type: "ACCREC",
          contact: {
            name: orderData.user_name || "Customer",
            emailAddress: orderData.user_email || "<EMAIL>"
          },
          date: new Date().toISOString().split('T')[0],
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          lineItems: lineItems,
          status: "AUTHORISED",
          reference: `Order #${orderData.order_id || "Unknown"}`,
          lineAmountTypes: "Exclusive", // Tax is added to the line amounts
        }]
      };
      
      console.log("Sending invoice data to Xero:", JSON.stringify(invoiceData));
      
      // Create the invoice using the SDK with a type assertion
      try {
        const response = await xero.accountingApi.createInvoices(tenantId, invoiceData as any);
        console.log("Xero invoice created successfully");
        
        if (response.body && response.body.invoices && response.body.invoices.length > 0) {
          return response.body.invoices[0];
        } else {
          console.error("Xero API returned success but no invoice data");
          throw new Error("No invoice data in Xero response");
        }
      } catch (apiError) {
        console.error("Xero API error:", apiError.response ? JSON.stringify(apiError.response.body) : apiError.message);
        throw new Error(`Xero API error: ${apiError.message}`);
      }
    } catch (error) {
      console.error("Error in createInvoice:", error);
      throw new Error(`Failed to create Xero invoice: ${error.message}`);
    }
  },
  async markInvoiceAsPaid(invoiceId, amount, accountCode = '605', paymentDate = null) {
    try {
      // Get authenticated client
      const xero = await this.getAuthenticatedClient();
      await xero.updateTenants();
      const tenants = xero.tenants;
      
      if (!tenants || tenants.length === 0) {
        console.error("No Xero tenants available");
        throw new Error("No Xero tenants available");
      }
      
      const tenantId = tenants[0].tenantId;
      console.log(`Using Xero tenant: ${tenantId}`);
      
      // Use today's date if no payment date is provided
      const date = paymentDate || new Date().toISOString().split('T')[0];
      
      // Create payment object according to Xero API requirements
      // Note: amount is expected as a string
      const payment = {
        payments: [{
          invoice: {
            invoiceID: invoiceId
          },
          account: {
            code: accountCode
          },
          date: date,
          amount: amount  // Already as string from caller
        }]
      };
      
      console.log(`Attempting to mark invoice ${invoiceId} as paid with amount ${amount}`);
      
      // Call the Xero API to create a payment with type assertion
      const response = await xero.accountingApi.createPayments(
        tenantId,
        payment as any
      );
      
      console.log(`Invoice ${invoiceId} marked as paid successfully`);
      return {
        success: true,
        paymentId: response.body?.payments?.[0]?.paymentID,
        date: date,
        amount: amount
      };
    } catch (error) {
      // Format error for better debugging
      let errorDetails = "undefined";
      if (error.response && error.response.body) {
        try {
          errorDetails = JSON.stringify(error.response.body);
        } catch (e) {
          errorDetails = error.response.body.toString();
        }
      } else if (error.message) {
        errorDetails = error.message;
      }
      
      console.error(`Error marking Xero invoice as paid: ${errorDetails}`);
      throw new Error(`Failed to mark Xero invoice as paid: ${error.message}`);
    }
  },
  
  /**
   * Email an invoice from Xero
   */
  async emailInvoice(invoiceId, emailAddress) {
    try {
      const xero = await this.getAuthenticatedClient();
      
      if (!xero || !xero.tenants || xero.tenants.length === 0) {
        throw new Error('No authorized Xero tenants found');
      }
      
      const tenant = xero.tenants[0];
      console.log(`Using Xero tenant: ${tenant.tenantId}`);
      
      // Create empty request object as required
      const requestEmpty = {}; 
      
      // Generate a timestamp-based unique identifier to avoid idempotency key conflicts
      const timestamp = new Date().getTime();
      // Make the email unique by appending a timestamp
      const uniqueEmail = `${emailAddress}?timestamp=${timestamp}`;
      
      console.log(`Attempting to email invoice ${invoiceId} to ${emailAddress} (using unique identifier)`);
      
      // Call with proper parameter order and unique email to avoid idempotency conflicts
      await xero.accountingApi.emailInvoice(
        tenant.tenantId,
        invoiceId,
        requestEmpty,
        uniqueEmail  // Using unique email with timestamp
      );
      
      console.log(`Invoice ${invoiceId} emailed to ${emailAddress} successfully`);
      return { success: true };
    } catch (error) {
      // Try to extract more specific error information
      let errorDetails = "undefined";
      if (error.response && error.response.body) {
        try {
          errorDetails = JSON.stringify(error.response.body);
        } catch (e) {
          errorDetails = error.response.body.toString();
        }
      } else if (error.message) {
        errorDetails = error.message;
      }
      
      console.error(`Error emailing Xero invoice: ${errorDetails}`);
      throw new Error(`Failed to email Xero invoice: ${error.message}`);
    }
  },
  
  /**
   * Handle the full invoice workflow - create and email
   */
  async processOrderInvoice(orderData) {
    try {
      // Step 1: Create invoice if possible
      const invoice = await this.createInvoice(orderData);
      
      // If no invoice was created (e.g., no order items), return a graceful response
      if (!invoice) {
        console.log("No invoice was created - likely no order items available");
        return { 
          success: false, 
          reason: "no_order_items",
          message: "No order items available for invoice creation" 
        };
      }
      
      // Access properties in a case-insensitive way
      // This handles both SDK types and API responses
      const invoiceId = invoice.invoiceID || invoice.invoiceID;
      const invoiceNumber = invoice.invoiceNumber || invoice.invoiceNumber;
      
      if (invoiceId) {
        // Calculate the total invoice amount from order items
        let totalAmount = 0;
        
        // Add up all line items including GST
        if (orderData.order_items && orderData.order_items.length > 0) {
          orderData.order_items.forEach(item => {
            const amount = (item.discounted_price || item.price || 0);
            const quantity = item.quantity || 1;
            // Include the item price plus GST (10%)
            totalAmount += amount * quantity * 1.1;
          });
        }
        
        // Add shipping cost if exists (including GST)
        if (orderData.shipping_cost && parseFloat(orderData.shipping_cost) > 0) {
          totalAmount += parseFloat(orderData.shipping_cost) * 1.1;
        }
        
        // Alternatively, use the invoice.total if available
        if (invoice.total) {
          // If invoice.total is a string, parse it to a number
          if (typeof invoice.total === 'string') {
            totalAmount = parseFloat(invoice.total);
          } else {
            totalAmount = Number(invoice.total);
          }
        }
        
        // Round to 2 decimal places for currency
        totalAmount = Math.round(totalAmount * 100) / 100;
        
        // IMPORTANT: Convert to string for the API call
        // This is the key fix for the type error
        const amountAsString = totalAmount.toString();
        
        // Step 2: Mark invoice as paid using today's date
        console.log(`Marking invoice ${invoiceId} as paid with amount ${totalAmount}`);
        
        const paymentResult = await this.markInvoiceAsPaid(
          invoiceId,
          amountAsString,  // Using string version explicitly
          '605',  // Default Stripe account code
          new Date().toISOString().split('T')[0]  // Today's date
        );
        
        // Step 3: Extract email from order data and send email AFTER marking as paid
        const userEmail = orderData.user?.email || orderData.user_email;
        let emailSent = false;
        
        if (userEmail) {
          // Pass both invoiceId and email to the emailInvoice method
          console.log(`Emailing invoice ${invoiceId} to ${userEmail}`);
          await this.emailInvoice(invoiceId, userEmail);
          emailSent = true;
        } else {
          console.log(`Cannot email invoice ${invoiceId} - no email address found in order data`);
        }
        
        return { 
          success: true, 
          invoiceId, 
          invoiceNumber,
          emailSent,
          paymentStatus: paymentResult.success ? 'paid' : 'unpaid',
          paymentId: paymentResult.paymentId,
          amount: totalAmount  // Return as number in the response
        };
      } else {
        throw new Error("Invoice created but no ID returned");
      }
    } catch (error) {
      console.error("Error processing order invoice:", error);
      throw new Error(`Failed to process Xero invoice: ${error.message || error}`);
    }
  }
}));