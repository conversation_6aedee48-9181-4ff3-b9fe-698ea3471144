import { factories } from '@strapi/strapi';

// Add XeroConfig interface
interface XeroConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string;
}

// Define interfaces for type safety
interface XeroTokenSet {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  id_token?: string;
}

export default factories.createCoreController('api::xero-token.xero-token', ({ strapi }) => ({
  // Add Xero authentication methods
  async authorize(ctx) {
    try {
      console.log('Starting Xero authorization flow');
      
      // Get environment variables directly
      const clientId = process.env.XERO_CLIENT_ID;
      const redirectUri = process.env.XERO_REDIRECT_URI;
      
      if (!clientId || !redirectUri) {
        throw new Error('Missing required Xero configuration (CLIENT_ID or REDIRECT_URI)');
      }
      
      console.log('Using environment credentials for Xero authorization');
      
      // Manually build consent URL
      const scopes = 'accounting.transactions accounting.settings offline_access';
      const encodedRedirectUri = encodeURIComponent(redirectUri);
      const encodedScope = encodeURIComponent(scopes);
      
      console.log('client id:',clientId);
      console.log('redirectUri :',redirectUri );
      const consentUrl = `https://login.xero.com/identity/connect/authorize?client_id=${clientId}&scope=${encodedScope}&response_type=code&redirect_uri=${encodedRedirectUri}`;
      
      console.log('Consent URL generated:', consentUrl);
      
      // Return URL for frontend to redirect user
      ctx.body = { url: consentUrl };
    } catch (error) {
      console.error('Xero authorize error:', error);
      // Log the full error details
      console.error('Error details:', JSON.stringify(error, null, 2));
      ctx.throw(500, error.message);
    }
  },

  async callback(ctx) {
    try {
      console.log('Xero callback received with query params:', JSON.stringify(ctx.query));
      
      const xeroService = strapi.service('api::xero-token.xero-token');
      const xeroClient = xeroService.getXeroClient();
      
      const { code } = ctx.query;
      
      if (!code) {
        return ctx.throw(400, 'Authorization code is missing');
      }
      
      // Type assertion for code
      const authCode = code as string;
      console.log('Authorization code received:', authCode.substring(0, 10) + '...');
      
      try {
        const tokenUrl = 'https://identity.xero.com/connect/token';
        let xeroConfig = strapi.config.get('xero') as XeroConfig;

        // Log raw env variables for debugging
        console.log('ENV XERO_CLIENT_ID:', process.env.XERO_CLIENT_ID);
        console.log('ENV XERO_CLIENT_SECRET:', process.env.XERO_CLIENT_SECRET);
        console.log('ENV XERO_REDIRECT_URI:', process.env.XERO_REDIRECT_URI);

        // Trim config values to avoid whitespace issues
        xeroConfig = {
          clientId: xeroConfig.clientId?.trim(),
          clientSecret: xeroConfig.clientSecret?.trim(),
          redirectUri: xeroConfig.redirectUri?.trim(),
          scopes: xeroConfig.scopes
        };

        // Log the trimmed config values
        console.log('Trimmed Xero Config:', xeroConfig);

        // Log the redirect_uri used in consent and token exchange
        console.log('Consent redirect_uri:', xeroConfig.redirectUri);
        console.log('Token exchange redirect_uri:', xeroConfig.redirectUri);

        // Check for whitespace or encoding issues
        if (
          !xeroConfig.clientId ||
          !xeroConfig.clientSecret ||
          !xeroConfig.redirectUri ||
          xeroConfig.clientId.trim() !== xeroConfig.clientId ||
          xeroConfig.clientSecret.trim() !== xeroConfig.clientSecret
        ) {
          console.error('Xero credentials have leading/trailing whitespace or are missing:', {
            clientId: xeroConfig.clientId,
            clientSecret: xeroConfig.clientSecret,
            redirectUri: xeroConfig.redirectUri
          });
          throw new Error('Xero credentials are missing or have whitespace issues');
        }

        // Validate credentials before using them
        if (!xeroConfig?.clientId || !xeroConfig?.clientSecret) {
          console.error('Missing Xero credentials:', {
            hasClientId: Boolean(xeroConfig?.clientId),
            hasClientSecret: Boolean(xeroConfig?.clientSecret),
            clientIdLength: xeroConfig?.clientId?.length,
            secretLength: xeroConfig?.clientSecret?.length
          });
          throw new Error('Xero client credentials are not properly configured');
        }

        // Create basic auth directly from config values
        const credentials = `${xeroConfig.clientId}:${xeroConfig.clientSecret}`;
        const basicAuth = Buffer.from(credentials).toString('base64');

        // Prepare token request body
        const tokenBody = new URLSearchParams({
          grant_type: 'authorization_code',
          code: authCode,
          redirect_uri: xeroConfig.redirectUri
        }).toString();

        // Log the full request body and headers
        console.log('Token request body:', tokenBody);
        console.log('Token request headers:', {
          'Authorization': `Basic ${basicAuth.substring(0, 12)}...`,
          'Content-Type': 'application/x-www-form-urlencoded'
        });

        // Rest of token exchange code
        const tokenResponse = await fetch(tokenUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${basicAuth}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: tokenBody
        });
        
        console.log('Token response status:', tokenResponse.status);

        // Add detailed response logging
        console.log('Raw Response Headers:', Object.fromEntries(tokenResponse.headers.entries()));
        
        if (!tokenResponse.ok) {
          const errorText = await tokenResponse.text();
          console.error('Token Exchange Error Details:', {
            status: tokenResponse.status,
            statusText: tokenResponse.statusText,
            headers: Object.fromEntries(tokenResponse.headers.entries()),
            errorBody: errorText
          });
          return ctx.throw(500, `Token exchange failed: ${tokenResponse.status} ${errorText}`);
        }
        
        // Type assertion for the JSON response
        const tokenSet = await tokenResponse.json() as XeroTokenSet;
        console.log('Token exchange successful, received keys:', Object.keys(tokenSet).join(', '));
        
        if (!tokenSet || !tokenSet.access_token) {
          return ctx.throw(500, `Xero did not return an access token. Response keys: ${Object.keys(tokenSet || {}).join(', ')}`);
        }
        
        // Store tokens in the database
        const tokenData = {
          acessToken: tokenSet.access_token, // Using misspelled property name to match schema
          refreshToken: tokenSet.refresh_token,
          expiresAt: new Date(new Date().getTime() + tokenSet.expires_in * 1000),
          tenantId: ''
        };
        
        // Set up the tokenSet in xeroClient for tenant operations
        xeroClient.setTokenSet({
          access_token: tokenSet.access_token,
          refresh_token: tokenSet.refresh_token,
          expires_in: tokenSet.expires_in,
          token_type: tokenSet.token_type
        });
        
        // Get tenant details
        try {
          await xeroClient.updateTenants();
          
          if (xeroClient.tenants && xeroClient.tenants.length > 0) {
            console.log('Found tenants:', xeroClient.tenants.length);
            tokenData.tenantId = xeroClient.tenants[0].tenantId;
          } else {
            console.log('No tenants found');
          }
        } catch (tenantError) {
          console.error('Error updating tenants:', tenantError.message);
          // Continue despite tenant error
        }
        
        console.log('Saving token data');
        
        // Save tokens to database
        const savedToken = await strapi.entityService.create('api::xero-token.xero-token', {
          data: tokenData
        });
        
        console.log('Token saved with ID:', savedToken.id);
        
        // Return success
        ctx.body = { success: true, message: 'Xero authentication successful' };
      } catch (tokenError) {
        console.error('Token exchange error details:', {
          message: tokenError.message,
          credentialsCheck: {
            configPresent: Boolean(strapi.config.get('xero')),
            envClientId: process.env.XERO_CLIENT_ID?.substring(0, 4),
            envClientSecret: process.env.XERO_CLIENT_SECRET?.substring(0, 4)
          }
        });
        console.error('Error stack:', tokenError.stack);
        return ctx.throw(500, `Failed to exchange code for tokens: ${tokenError.message}`);
      }
    } catch (error) {
      console.error('Xero callback error:', error);
      ctx.throw(500, error.message);
    }
  }
}));