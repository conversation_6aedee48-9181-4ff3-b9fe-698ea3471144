{"kind": "collectionType", "collectionName": "stripe_transactions", "info": {"singularName": "stripe-transaction", "pluralName": "stripe-transactions", "displayName": "Stripe Transaction", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"order_fk": {"type": "relation", "relation": "oneToOne", "target": "api::order.order", "inversedBy": "stripe_transaction_fk"}, "stripe_transaction_id": {"type": "string"}, "amount_to_charge": {"type": "decimal"}, "initiate_date": {"type": "datetime"}, "completed_date": {"type": "datetime"}, "error_reason": {"type": "string"}, "attempts": {"type": "integer"}, "stripe_transaction_status": {"type": "string"}, "stripe_payment_id": {"type": "string"}}}