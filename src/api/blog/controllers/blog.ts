// path: src/api/blog/controllers/blog.ts
import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';

export default factories.createCoreController('api::blog.blog', ({ strapi }) => ({
  // Keep default controller actions
  
  // Add custom controller action to update a blog by ID
  async updateBlogById(ctx: Context, next: Next) {
    const { id } = ctx.params;
    
    if (!id) {
      return ctx.badRequest('Blog ID is required in the URL parameters.');
    }
    
    const { data } = ctx.request.body || {};
    
    if (!data || typeof data !== 'object') {
      return ctx.badRequest('Data object is required in the request body.');
    }
    
    try {
      // Check if blog exists
      const existingBlog = await strapi.entityService.findOne('api::blog.blog', id);
      
      if (!existingBlog) {
        return ctx.notFound('Blog not found with the provided ID.');
      }
      
      // Update the blog
      const updatedBlog = await strapi.entityService.update('api::blog.blog', id, {
        data: {
          Title: data.Title,
          meta_description: data.meta_description,
          reading_time_minutes: data.reading_time_minutes,
          tags: data.tags,
          show_on_homepage: data.show_on_homepage,
          show_on_logged_in_dashboard: data.show_on_logged_in_dashboard,
          image_details: data.image_details,
          blog_content: data.blog_content,
          blog_status: data.blog_status,
          blog_category_fk: data.blog_category_fk,
          views: data.views
        },
      });
      
      return ctx.send({
        data: updatedBlog,
        message: 'Blog updated successfully'
      });
    } catch (err: any) {
      console.error('Error updating blog:', err);
      return ctx.internalServerError(`An error occurred: ${err.message}`);
    }
  }
}));