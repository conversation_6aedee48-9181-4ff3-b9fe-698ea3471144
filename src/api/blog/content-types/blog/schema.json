{"kind": "collectionType", "collectionName": "blogs", "info": {"singularName": "blog", "pluralName": "blogs", "displayName": "Blog", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"Title": {"type": "string"}, "meta_description": {"type": "string"}, "reading_time_minutes": {"type": "integer"}, "tags": {"type": "string"}, "show_on_homepage": {"type": "boolean"}, "show_on_logged_in_dashboard": {"type": "boolean"}, "image_details": {"type": "json"}, "blog_content": {"type": "text"}, "blog_status": {"type": "string"}, "blog_category_fk": {"type": "relation", "relation": "oneToOne", "target": "api::blog-category.blog-category"}, "views": {"type": "integer"}}}