{"kind": "collectionType", "collectionName": "orders", "info": {"singularName": "order", "pluralName": "orders", "displayName": "Order", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"user_fk": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "sub_total": {"type": "decimal"}, "shipping_cost": {"type": "decimal"}, "total_amount": {"type": "decimal"}, "stripe_transaction_fk": {"type": "relation", "relation": "oneToOne", "target": "api::stripe-transaction.stripe-transaction", "mappedBy": "order_fk"}, "order_name": {"type": "string"}, "order_status": {"type": "string"}, "xero_invoice_reference": {"type": "text"}, "xero_invoice_ID": {"type": "string"}}}