// src/api/order/routes/custom-order.ts

export default {
  routes: [
    {
      method: 'GET',
      path: '/orders/user/:id',
      handler: 'order.findByUser',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get all orders for a specific user',
      },
    },
    {
      method: 'GET',
      path: '/orders/all',
      handler: 'order.findAll',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get all orders with extended relationships',
      },
    },
  ],
};