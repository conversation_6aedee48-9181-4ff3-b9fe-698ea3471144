// src/api/order/controllers/order.ts
import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';

export default factories.createCoreController('api::order.order', ({ strapi }) => ({
  // Maintain all existing core controller methods
  
  // Add custom method to find orders by user_fk
  async findByUser(ctx: Context) {
    try {
      const { id } = ctx.params;
      
      if (!id) {
        return ctx.badRequest('User ID is required');
      }
      
      // Extract query parameters for pagination
      const { page = 1, pageSize = 25, sort = 'createdAt:desc' } = ctx.query as any;
      
      // Ensure values are numbers
      const pageNum = parseInt(String(page), 10);
      const pageSizeNum = parseInt(String(pageSize), 10);
      
      // Get the content type schema to find the correct relation name
      const orderContentType = strapi.contentTypes['api::order.order'];
      console.log('Order content type schema:', JSON.stringify(orderContentType.attributes, null, 2));
      
      // First, get the orders
      const query = {
        filters: {
          user_fk: id
        },
        sort: [sort],
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum
        }
      };
      
      const orders = await strapi.entityService.findMany('api::order.order', query as any);
      
      // Then, for each order, get its order items
      const ordersWithItems = await Promise.all(orders.map(async (order) => {
        const orderItemsQuery = {
          filters: {
            order_fk: order.id
          },
          populate: {
            product_fk: true,
            supplier_fk: true
          }
        };
        
        const orderItems = await strapi.entityService.findMany('api::order-item.order-item', orderItemsQuery as any);
        
        return {
          ...order,
          order_items: orderItems
        };
      }));
      
      // Count total for pagination
      const count = await strapi.db.query('api::order.order').count({
        where: {
          user_fk: id
        }
      });
      
      return {
        data: ordersWithItems,
        meta: {
          pagination: {
            page: pageNum,
            pageSize: pageSizeNum,
            pageCount: Math.ceil(count / pageSizeNum),
            total: count
          }
        }
      };
    } catch (error: any) {
      console.error('Error fetching user orders:', error);
      return ctx.internalServerError(`An error occurred: ${error.message}`);
    }
  },

  // Add the findAll method to the controller to get all orders with extended relationships
  async findAll(ctx: Context) {
    try {
      // Extract query parameters for pagination
      const { page = 1, pageSize = 25, sort = 'createdAt:desc' } = ctx.query as any;
      
      // Ensure values are numbers
      const pageNum = parseInt(String(page), 10);
      const pageSizeNum = parseInt(String(pageSize), 10);
      
      // First, get all orders with user_fk populated
      const query = {
        populate: {
          user_fk: true,
          stripe_transaction_fk: true
        },
        sort: [sort],
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum
        }
      };
      
      const orders = await strapi.entityService.findMany('api::order.order', query as any);
      
      // Then, for each order, get its order items
      const ordersWithItems = await Promise.all(orders.map(async (order) => {
        const orderItemsQuery = {
          filters: {
            order_fk: order.id
          },
          populate: {
            product_fk: true,
            supplier_fk: true
          }
        };
        
        const orderItems = await strapi.entityService.findMany('api::order-item.order-item', orderItemsQuery as any);
        
        return {
          ...order,
          order_items: orderItems
        };
      }));
      
      // Count total for pagination
      const count = await strapi.db.query('api::order.order').count();
      
      return {
        data: ordersWithItems,
        meta: {
          pagination: {
            page: pageNum,
            pageSize: pageSizeNum,
            pageCount: Math.ceil(count / pageSizeNum),
            total: count
          }
        }
      };
    } catch (error: any) {
      console.error('Error fetching all orders:', error);
      return ctx.internalServerError(`An error occurred: ${error.message}`);
    }
  }
}));