export default {
  routes: [
    {
      method: 'POST',
      path: '/stripe',
      handler: 'stripe-checkout.index',
      config: {
        policies: [],
        middlewares: [],
        auth: false, // change this when integrate
      },
    },
    {
      method: 'POST',
      path: '/stripe/checkout',
      handler: 'stripe-checkout.createCheckoutSession',
      config: {
        policies: [],
        middlewares: [],
        auth: false, // change this when integrate
      },
    },
    {
      method: 'GET',
      path: '/stripe/checkout-success',
      handler: 'stripe-checkout.checkoutSuccess',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    }, 
    {
      method: 'GET',
      path: '/stripe/checkout-cancel',
      handler: 'stripe-checkout.checkoutCancel',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    }, 
  ],
};