import Stripe from 'stripe';

export interface StripeSettings {
  mode: 'live' | 'test'
}

const getStripeInstance = async (mode: 'live' | 'test') => {
  const envVar =
    mode === 'live'
      ? 'STRAPI_ADMIN_LIVE_STRIPE_SECRET_KEY'
      : 'STRAPI_ADMIN_TEST_STRIPE_SECRET_KEY';

  const secretKey = process.env[envVar];
  if (!secretKey) {
    throw new Error(`[Stripe] Missing ${envVar} in env`);
  }

  return new Stripe(secretKey as string);
};

export default () => ({
  createCheckoutSession: async (params: Stripe.Checkout.SessionCreateParams) => {
    const settings = await strapi.entityService.findMany(
      'api::stripe-setting.stripe-setting',
      {
        limit: 1
      }
    );

    const mode = settings?.[0]?.use_live_mode ? 'live' : 'test';

    const stripe = await getStripeInstance(mode);

    return await stripe.checkout.sessions.create(params);
  },
  
  retrieveCheckoutSession: async (session_id: string) => {
    const settings = await strapi.entityService.findMany(
        'api::stripe-setting.stripe-setting',
        {
          limit: 1
        }
    );

    const mode = settings?.[0]?.use_live_mode ? 'live' : 'test';
    
    const stripe = await getStripeInstance(mode);

    return await stripe.checkout.sessions.retrieve(session_id);
  },

  getStripeSettings: async () => {
    const settings = await strapi.entityService.findMany(
      'api::stripe-setting.stripe-setting',
      {limit: 1}
    );

    if (!settings || !settings?.[0]) {
      return {mode: 'test'};
    }

    return settings[0];
  },

  updateSettings: async({mode}: StripeSettings) => {
    const existing = await strapi.entityService.findMany(
      'api::stripe-setting.stripe-setting',
      { limit: 1 }
    );

    if (existing != null) {
      await strapi.entityService.update(
        'api::stripe-setting.stripe-setting',
        existing[0].id,
        // @ts-ignore
        { data: { use_live_mode: mode === 'live' } }
      );
    } else {
      await strapi.entityService.create(
        'api::stripe-setting.stripe-setting',
        { data: { use_live_mode: mode === 'live' } }
      );
    }
  }
});

