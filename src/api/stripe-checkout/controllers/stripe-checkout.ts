import type {Core} from '@strapi/strapi';
import {str} from "ajv";   

const StripeCheckoutController = ({strapi}: { strapi: Core.Strapi }) => ({
    index(ctx) {
        ctx.send('hello from stripe checkout');
    },

    async createCheckoutSession(ctx) {
        try {
            // Extract data from request body
            const { data } = ctx.request.body;
            if (!data) {
                ctx.send('Order data is required', 400);
                return;
            }
            const { user_fk, sub_total, shipping_cost, total_amount, order_items } = data;
            if (!user_fk || !sub_total) {
                ctx.send('User ID and subtotal are required', 400);
                return;
            }
            if (!order_items || !Array.isArray(order_items) || order_items.length === 0) {
                ctx.send('Order items are required', 400);
                return;
            }
            // Extract product names from order items to create order_name
            const productNames = order_items.map(item => item.product_name || 'Unknown Product').filter(name => name);
            const order_name = productNames.join(', ');
            // Use the subtotal provided from the frontend as it already includes quantity calculations
            let calculatedSubTotal = sub_total;
            // If shipping_cost is provided at the order level, use that, otherwise default to 0
            const finalShippingCost = shipping_cost || 0;
            // Add 10% GST
            const preTaxProductAmount = calculatedSubTotal / 1.1;
            const gstAmount = preTaxProductAmount * 0.1;
            // Calculate final total with GST
            const finalTotal = calculatedSubTotal + finalShippingCost;
            // Create a new order
            const newOrder = await strapi.entityService.create('api::order.order', {
                data: {
                    user_fk,
                    sub_total: calculatedSubTotal,
                    shipping_cost: finalShippingCost,
                    gst_amount: gstAmount, // Store GST amount separately if your schema supports it
                    total_amount: finalTotal,
                    order_name, // Add the order_name field with comma-separated product names
                    order_status: 'pending' // Initial status when order is created
                }
            });
            if (!newOrder || !newOrder.documentId) {
                ctx.throw(500, 'Failed to create order');
                return;
            }
            const order_id = newOrder.documentId;
            // Create order items
            for (const item of order_items) {
                await strapi.entityService.create('api::order-item.order-item', {
                    data: {
                        product_fk: item.product_fk,
                        product_name: item.product_name, // Store the product_name in order items
                        price: item.price,
                        discounted_price: item.discounted_price,
                        quantity: item.quantity,
                        order_fk: order_id,
                        supplier_fk: item.supplier_fk,
                        shipping_cost: item.shipping_cost,
                        notes: item.notes,
                        size: item.size
                    }
                });
            }
            // Load order to ensure it's properly created and retrievable
            const order = await strapi.documents('api::order.order')
                .findFirst({
                    filters: {
                        documentId: order_id
                    }
                });
            if (!order) {
                ctx.throw(500, 'Order created but cannot be retrieved');
                return;
            }
            // Set the amount to charge based on our calculation with GST
            let amount = finalTotal;
            let currency = 'AUD';
            // Create line items for each product in the order - start with an empty array
            let line_items = [];
            // Add product line items
            const productLineItems = order_items.map(item => {
                // IMPORTANT FIX: The price already includes the total for the quantity
                // We need to divide by the quantity to get the unit price for Stripe
                const unitPrice = item.discounted_price || item.price;
                const pricePerUnit = item.quantity > 1 ? unitPrice / item.quantity : unitPrice;
                return {
                    price_data: {
                        currency,
                        product_data: {
                            name: item.product_name || 'Product',
                            // You can also add images if you have them
                            // images: [item.product_image_url],
                        },
                        unit_amount: Math.round(pricePerUnit * 100), // Unit price in cents
                    },
                    quantity: item.quantity, // Stripe will multiply unit_amount by quantity
                };
            });
            // Add the product line items to our array
            line_items = [...line_items, ...productLineItems];
            // Add shipping as a separate line item if it exists
            if (finalShippingCost && finalShippingCost > 0) {
                line_items.push({
                    price_data: {
                        currency,
                        product_data: {
                            name: 'Shipping',
                        },
                        unit_amount: Math.round(finalShippingCost * 100),
                    },
                    quantity: 1,
                });
            }
            // Add GST as a separate line item for transparency
            if (gstAmount > 0) {
                line_items.push({
                    price_data: {
                        currency,
                        product_data: {
                            name: 'GST (10%)',
                        },
                        unit_amount: Math.round(gstAmount * 100),
                    },
                    quantity: 1,
                });
            }
            const session = await strapi
                .service('api::stripe-checkout.stripe-checkout')
                .createCheckoutSession({
                    payment_method_types: ['card'],
                    mode: 'payment',
                    line_items: line_items, // Use the array of line items
                    metadata: {
                        order_id: order.documentId, // Store document ID in metadata
                    },
                    // Add custom text to the checkout page (only in submit section to avoid shipping address requirement)
                    custom_text: {
                        submit: {
                            message: 'These are the items you are purchasing from Salus. Your payment is processed securely by Stripe.',
                        },
                    },
                    success_url: `${process.env.STRAPI_ADMIN_STRIPE_SUCCESS_URL}?session_id={CHECKOUT_SESSION_ID}`,
                    cancel_url: `${process.env.STRAPI_ADMIN_STRIPE_CANCEL_URL}?session_id={CHECKOUT_SESSION_ID}`,
                });
            const stripeTransaction = {
                order_fk: order.documentId, // Use document ID for relationship
                stripe_transaction_id: session.id,
                amount_to_charge: amount,
                initiate_date: new Date(),
                attempts: 1,
                stripe_transaction_status: session.status,
            };
            await strapi.entityService.create('api::stripe-transaction.stripe-transaction', {
                data: {
                    ...stripeTransaction
                }
            });
            ctx.send({stripeCheckoutUrl: session.url});
        } catch (err) {
            ctx.throw(500, err.message);
        }
    },
     
    async checkoutSuccess(ctx) {
        const { session_id } = ctx.request.query;
        
        console.log("==== CHECKOUT SUCCESS START ====");
        console.log("Processing session:", session_id);
        
        if (!session_id) {
            console.log("ERROR: No session ID provided");
            return ctx.badRequest('Session ID is required in the query parameters.');
        }
        
        try {
            // Step 1: Find the transaction with this session ID
            console.log("DEBUG: About to find transaction");
            const transaction = await strapi.entityService.findMany('api::stripe-transaction.stripe-transaction' as any, {
                filters: { stripe_transaction_id: session_id },
                populate: ['order_fk']
            }) as any[];
            
            console.log("DEBUG: Transaction search result:", transaction ? `Found ${transaction.length} transactions` : "No transaction found");
            
            // If no transaction is found, return error
            if (!transaction || transaction.length === 0) {
                console.log("ERROR: No transaction found with session ID:", session_id);
                return ctx.badRequest('No transaction found with the provided session ID.');
            }
            
            console.log("DEBUG: Transaction ID:", transaction[0].id);
            console.log("DEBUG: Order FK present:", transaction[0].order_fk ? "Yes" : "No");
            if (transaction[0].order_fk) {
                console.log("DEBUG: Order ID:", transaction[0].order_fk.id);
            }
            
            // Step 2: Get the checkout session from Stripe
            console.log("DEBUG: About to retrieve Stripe session using key:", 
                process.env.STRAPI_ADMIN_TEST_STRIPE_SECRET_KEY ? "API Key available (length: " + 
                process.env.STRAPI_ADMIN_TEST_STRIPE_SECRET_KEY.length + ")" : "API Key missing");
                
            const stripe = require('stripe')(process.env.STRAPI_ADMIN_TEST_STRIPE_SECRET_KEY);
            const session = await stripe.checkout.sessions.retrieve(session_id);
            console.log("DEBUG: Stripe session retrieved successfully");
            console.log("DEBUG: Payment status:", session.payment_status);
            console.log("DEBUG: Payment intent:", session.payment_intent);
            
            // Step 3: Update the transaction status to 'completed'
            console.log("STEP 3: Updating transaction status to 'completed'");
            await strapi.entityService.update('api::stripe-transaction.stripe-transaction' as any, transaction[0].id, {
                data: {
                    status: 'completed',
                    payment_status: session.payment_status,
                    stripe_payment_intent_id: session.payment_intent,
                    meta: JSON.stringify(session)
                }
            });
            console.log("DEBUG: Transaction status updated successfully");
            
            // Step 4: Get the order associated with this transaction
            console.log("STEP 4: Finding associated order");
            if (!transaction[0].order_fk || !transaction[0].order_fk.id) {
                console.error("ERROR: No order associated with this transaction");
                return ctx.badRequest('No order associated with this transaction.');
            }
            
            const order = await strapi.entityService.findOne('api::order.order' as any, transaction[0].order_fk.id, {
                populate: ['user_fk']
            }) as any;
            
            console.log("DEBUG: Order found:", order ? "Yes" : "No");
            if (order) {
                console.log("DEBUG: Order ID:", order.id);
                console.log("DEBUG: Order has user:", order.user_fk ? "Yes" : "No");
            }
            
            // Step 5: Update the order status to 'paid'
            console.log("STEP 5: Updating order status to 'paid'");
            await strapi.entityService.update('api::order.order' as any, order.id, {
                data: { order_status: 'paid' }
            });
            console.log("DEBUG: Order status updated to 'paid'");
            
            // Step 5.1: Send order notification email to admin
            console.log("STEP 5.1: Sending order notification email to admin");
            try {
                // Get the updated order with user details to have the most current data
                const updatedOrder = await strapi.entityService.findOne('api::order.order' as any, order.id, {
                    populate: ['user_fk']
                }) as any;
                
                // Query order items separately - fixes the validation error
                console.log("DEBUG: Querying order items separately");
                const orderItems = await strapi.db.query('api::order-item.order-item').findMany({
                    where: { order_fk: order.id },
                    populate: ['product_fk']
                });
                
                // Create the login URL for admin
                const loginUrl = process.env.FRONTEND_LOGIN_URL || 'http://localhost:3000/login';
                
                // Send email using SendGrid API
                console.log("DEBUG: Preparing to send admin notification email");
                console.log (process.env.SENDGRID_API_KEY);
                const emailResponse = await fetch('https://api.sendgrid.com/v3/mail/send', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${process.env.SENDGRID_API_KEY || '*********************************************************************'}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        personalizations: [{
                            to: [{ email: process.env.ADMIN_NOTIFICATION_EMAIL || '<EMAIL>' }],
                            dynamic_template_data: {
                                orderId: order.id,
                                orderName: updatedOrder.order_name,
                                orderStatus: 'paid',
                                totalAmount: updatedOrder.total_amount,
                                customerName: updatedOrder.user_fk?.username || "Customer",
                                customerEmail: updatedOrder.user_fk?.email || "No email provided",
                                orderDate: new Date().toISOString().split('T')[0],
                                itemCount: orderItems.length,
                                loginUrl: loginUrl
                            }
                        }],
                        from: {
                            email: '<EMAIL>',
                            name: 'Salus'
                        },
                        template_id: 'd-3eaf6557563b4017ae661ca8d724c692',
                        tracking_settings: {
                            click_tracking: {
                                enable: false
                            }
                        }
                    })
                });
                
                if (!emailResponse.ok) {
                    const errorData = await emailResponse.json();
                    console.error('SendGrid email error:', errorData);
                    // Continue even if email fails
                } else {
                    console.log('Admin order notification email sent successfully');
                }
            } catch (emailError) {
                console.error('Error sending admin order notification email:', emailError);
                console.error('Error details:', emailError.message);
                // Continue with checkout process even if email sending fails
            }
            
            // Add safety check for user_fk
            if (!order.user_fk || !order.user_fk.id) {
                console.log("WARNING: No user associated with this order");
            } else {
                // Steps 6 & 7: Update cart items status
                console.log("STEP 6: Finding user cart items");
                const userId = order.user_fk.id;
                console.log("DEBUG: User ID:", userId);
                
                const cartItems = await strapi.entityService.findMany('api::cart.cart' as any, {
                    filters: {
                        user_fk: userId,
                        cart_status: 'active'
                    }
                }) as any[];
                
                console.log("DEBUG: Cart items found:", cartItems ? cartItems.length : 0);
                
                console.log("STEP 7: Updating cart items status");
                for (const item of cartItems) {
                    console.log("DEBUG: Updating cart item ID:", item.id);
                    await strapi.entityService.update('api::cart.cart' as any, item.id, {
                        data: { cart_status: 'order-completed' }
                    });
                }
                console.log("DEBUG: All cart items updated successfully");
            }
            
            // Step 8: Generate and email Xero invoice
            console.log("STEP 8: Generating and emailing Xero invoice");
            try {
                // First, get the order with user details
                console.log("DEBUG: About to query order with user details");
                const orderWithUser = await strapi.entityService.findOne('api::order.order' as any, order.id, {
                    populate: ['user_fk']
                }) as any;
                
                // Now directly query for order items linked to this order
                console.log("DEBUG: Performing direct query for order items");
                const orderItems = await strapi.db.query('api::order-item.order-item').findMany({
                    where: { order_fk: order.id },
                    populate: ['product_fk']
                });
                
                console.log("DEBUG: Order details found:", orderWithUser ? "Yes" : "No");
                console.log("DEBUG: Has order items:", orderItems ? `Yes (${orderItems.length})` : "No");
                console.log("DEBUG: Has user details:", orderWithUser?.user_fk ? "Yes" : "No");
                
                // Create combined orderWithDetails with both user data and order items
                const orderWithDetails = {
                    ...orderWithUser,
                    order_items: orderItems || []
                };
                
                // Format order data for Xero service
                const orderData = {
                    order_id: order.id,
                    user_name: orderWithUser.user_fk?.username || "Customer",
                    user_email: orderWithUser.user_fk?.email || "<EMAIL>",
                    order_name: orderWithUser.order_name,
                    sub_total: orderWithUser.sub_total,
                    shipping_cost: orderWithUser.shipping_cost,
                    total_amount: orderWithUser.total_amount,
                    order_items: (orderItems || []).map((item: any) => ({
                        product_name: item.product_name,
                        quantity: item.quantity,
                        price: item.price,
                        discounted_price: item.discounted_price
                    }))
                };
                
                // Add detailed diagnostics for order items
                if (orderItems && orderItems.length > 0) {
                    console.log("DEBUG: First order item:", {
                        id: orderItems[0].id,
                        product_name: orderItems[0].product_name,
                        quantity: orderItems[0].quantity,
                        price: orderItems[0].price
                    });
                }
                
                // Call the Xero service to process the invoice
                //console.log("DEBUG: Looking for Xero token service");
                //console.log("DEBUG: Available services:", Object.keys(strapi.services || {}));
                
                // Try different ways to access the Xero service
                let xeroService;
                try {
                    console.log("DEBUG: Trying standard service access...");
                    xeroService = strapi.services['api::xero-token.xero-token'];
                    console.log("DEBUG: Standard access result:", xeroService ? "Success" : "Failed");
                } catch (e) {
                    console.log("DEBUG: Standard access error:", e.message);
                }
                
                if (!xeroService) {
                    console.log("WARNING: Xero service not found, skipping invoice generation");
                } else {
                    // We found a service, try to use it
                    console.log("DEBUG: Xero service found, attempting to process invoice");
                    console.log("DEBUG: Order data being sent to Xero:", {
                        order_id: orderData.order_id,
                        user_email: orderData.user_email,
                        item_count: orderData.order_items.length,
                        total_amount: orderData.total_amount
                    });
                    
                    const invoiceResult = await xeroService.processOrderInvoice(orderData);
                    
                    if (invoiceResult && invoiceResult.success) {
                        console.log(`SUCCESS: Xero invoice ${invoiceResult.invoiceNumber} created and emailed`);
                        
                        // Update order with invoice information using your specific field names
                        await strapi.entityService.update('api::order.order' as any, order.id, {
                            data: {
                                xero_invoice_reference: invoiceResult.invoiceNumber,
                                xero_invoice_ID: invoiceResult.invoiceId
                            }
                        });
                        console.log("DEBUG: Order updated with invoice references");
                    } else {
                        console.error(`WARNING: Failed to create Xero invoice: ${invoiceResult?.error || 'Unknown error'}`);
                        // Continue with checkout flow despite invoice failure
                    }
                }
            } catch (xeroError) {
                // Log error but don't fail the checkout process
                console.error("ERROR in Xero invoice processing:", xeroError);
                console.error("Error stack:", xeroError.stack);
                console.log("Continuing checkout success flow despite Xero error");
            }
            
            // Step 9: Redirect to frontend
            console.log("STEP 9: About to redirect to frontend");
            console.log("DEBUG: Frontend URL from env:", process.env.FRONTEND_URL || "NOT SET");
            
            try {
                const redirectUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard/orders`;
                console.log("DEBUG: Redirecting to:", redirectUrl);
                console.log("==== CHECKOUT SUCCESS COMPLETE ====");
                return ctx.redirect(redirectUrl);
            } catch (redirectError) {
                console.error("ERROR during redirect:", redirectError);
                console.log("==== CHECKOUT SUCCESS COMPLETE WITH REDIRECT ERROR ====");
                // Fallback response if redirect fails
                return ctx.send({ success: true, message: "Checkout completed successfully" });
            }
        } catch (err) {
            console.error("CRITICAL ERROR in checkoutSuccess:", err);
            console.error("Error stack:", err.stack);
            return ctx.internalServerError(`An error occurred: ${err.message}`);
        }
    },
    async checkoutCancel(ctx) {
        const {session_id} = ctx.request.query;

        const transaction = await strapi.documents('api::stripe-transaction.stripe-transaction')
            .findFirst({
                filters: {
                    stripe_transaction_id: session_id
                }
            });

        if (!transaction) {
            ctx.throw(500, 'Transaction not found');
            return;
        }

        // Need to retrieve the full transaction to access the order relationship
        const fullTransaction = await strapi.entityService.findOne(
            'api::stripe-transaction.stripe-transaction', 
            transaction.id,
            { populate: ['order_fk'] }
        ) as any;

        if (fullTransaction && fullTransaction.order_fk) {
            // Get the order ID - could be an object or just the ID
            const orderId = typeof fullTransaction.order_fk === 'object' 
                ? fullTransaction.order_fk.documentId 
                : fullTransaction.order_fk;
            
            // Update the order status to failed
            await strapi.documents('api::order.order')
                .update({
                    documentId: orderId,
                    data: {
                        order_status: 'failed'
                    }
                });
        }

        // Remove the pending transaction. The new order checkout should start with a new transaction
        await strapi.documents('api::stripe-transaction.stripe-transaction')
            .delete({
                documentId: transaction.documentId,
            });

        ctx.send(`<h1>User Cancelled Transaction</h1>`);
    },

    async validateKey(ctx) {
        const {mode} = ctx.request.body;

        const envVar =
            mode === 'live'
                ? 'STRAPI_ADMIN_LIVE_STRIPE_SECRET_KEY'
                : 'STRAPI_ADMIN_TEST_STRIPE_SECRET_KEY';

        const secretKey = process.env[envVar];
        if (!secretKey) {
            ctx.throw(400, `${envVar} is not configured.`);
        }
         console.log('----------- STRIPE KEY VERIFICATION -----------');
         console.log(`Mode: ${mode}`);
         console.log(`Environment Variable: ${envVar}`);
         console.log(`Secret Key: ${secretKey}`);
         console.log('----------------------------------------------');

        const stripe = require('stripe')(secretKey);
        try {
            await stripe.accounts.retrieve();
            ctx.send({success: true});
        } catch (err) {
            ctx.throw(400, 'Invalid Stripe secret key' + ` ${secretKey}`);
        }
    },

    async getSettings(ctx) {
        const settings = await strapi
            .service('api::stripe-checkout.stripe-checkout')
            .getStripeSettings();

        ctx.send(settings);
    },

    async updateSettings(ctx) {
        const {mode} = ctx.request.body;

        await strapi
            .service('api::stripe-checkout.stripe-checkout')
            .updateSettings({mode});

        ctx.send({success: true});
    }
});

export default StripeCheckoutController;
