/**
 * supplier controller
 */
import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';

export default factories.createCoreController('api::supplier.supplier', ({ strapi }) => ({
  // Keep the default controller actions
  
  // Add custom controller action to update a supplier by ID
  async updateSupplierById(ctx: Context, next: Next) {
    const { id } = ctx.params;
    
    if (!id) {
      return ctx.badRequest('Supplier ID is required in the URL parameters.');
    }
    
    const { data } = ctx.request.body || {};
    
    if (!data || typeof data !== 'object') {
      return ctx.badRequest('Data object is required in the request body.');
    }
    
    try {
      // Check if supplier exists
      const existingSupplier = await strapi.entityService.findOne('api::supplier.supplier', id);
      
      if (!existingSupplier) {
        return ctx.notFound('Supplier not found with the provided ID.');
      }
      
      // Update the supplier
      const updatedSupplier = await strapi.entityService.update('api::supplier.supplier', id, {
        data: {
          supplier_name: data.supplier_name,
          email_address: data.email_address,
          shipping_rules: data.shipping_rules,
          supplier_status: data.supplier_status
        },
      });
      
      // Get product count for this supplier
      const productCount = await strapi.db.query('api::product.product').count({
        where: {
          supplier_fk: id
        }
      });
      
      // Add product count to the response
      return ctx.send({
        data: {
          ...updatedSupplier,
          product_count: productCount
        },
        message: 'Supplier updated successfully'
      });
    } catch (err: any) {
      console.error('Error updating supplier:', err);
      return ctx.internalServerError(`An error occurred: ${err.message}`);
    }
  },
  
  // Override find method to include product count
  async find(ctx: Context) {
    // Call the default find method
    const { data, meta } = await super.find(ctx);
    
    // For each supplier, count their products
    const enhancedData = await Promise.all(
      data.map(async (supplier: any) => {
        const productCount = await strapi.db.query('api::product.product').count({
          where: {
            supplier_fk: supplier.id
          }
        });
        
        return {
          ...supplier,
          attributes: {
            ...supplier.attributes,
            product_count: productCount
          }
        };
      })
    );
    
    return { data: enhancedData, meta };
  },
  
  // Override findOne method to include product count
  async findOne(ctx: Context) {
    // Call the default findOne method
    const { data, meta } = await super.findOne(ctx);
    
    // Count products for this supplier
    const productCount = await strapi.db.query('api::product.product').count({
      where: {
        supplier_fk: data.id
      }
    });
    
    // Add the count to the response
    return {
      data: {
        ...data,
        attributes: {
          ...data.attributes,
          product_count: productCount
        }
      },
      meta
    };
  }
}));