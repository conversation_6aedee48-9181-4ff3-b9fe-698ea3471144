// path: src/api/blog-category/controllers/blog-category.ts
import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';

export default factories.createCoreController('api::blog-category.blog-category', ({ strapi }) => ({
  // Keep existing find and findOne methods
  async find(ctx: Context) {
    // Call the default find method
    const { data, meta } = await super.find(ctx);
    
    // For each blog category, count their blogs
    const enhancedData = await Promise.all(
      data.map(async (blogCategory: any) => {
        const blogCount = await strapi.db.query('api::blog.blog').count({
          where: {
            blog_category_fk: blogCategory.id
          }
        });
        
        return {
          ...blogCategory,
          attributes: {
            ...blogCategory.attributes,
            blog_count: blogCount
          }
        };
      })
    );
    
    return { data: enhancedData, meta };
  },
  
  async findOne(ctx: Context) {
    // Call the default findOne method
    const { data, meta } = await super.findOne(ctx);
    
    // Count blogs for this category
    const blogCount = await strapi.db.query('api::blog.blog').count({
      where: {
        blog_category_fk: data.id
      }
    });
    
    // Add the count to the response
    return {
      data: {
        ...data,
        attributes: {
          ...data.attributes,
          blog_count: blogCount
        }
      },
      meta
    };
  },
  
  // Add new updateBlogCategoryById method
  async updateBlogCategoryById(ctx: Context, next: Next) {
    const { id } = ctx.params;
    
    if (!id) {
      return ctx.badRequest('Blog Category ID is required in the URL parameters.');
    }
    
    const { data } = ctx.request.body || {};
    
    if (!data || typeof data !== 'object') {
      return ctx.badRequest('Data object is required in the request body.');
    }
    
    try {
      // Check if blog category exists
      const existingBlogCategory = await strapi.entityService.findOne('api::blog-category.blog-category', id);
      
      if (!existingBlogCategory) {
        return ctx.notFound('Blog Category not found with the provided ID.');
      }
      
      // Update the blog category
      const updatedBlogCategory = await strapi.entityService.update('api::blog-category.blog-category', id, {
        data: {
          Title: data.title,
          // Add any other fields your blog-category might have
        },
      });
      
      // Get blog count for this category (since you already have this in your controller)
      const blogCount = await strapi.db.query('api::blog.blog').count({
        where: {
          blog_category_fk: id
        }
      });
      
      // Return the updated blog category with blog count
      return ctx.send({
        data: {
          ...updatedBlogCategory,
          blog_count: blogCount
        },
        message: 'Blog Category updated successfully'
      });
    } catch (err: any) {
      console.error('Error updating blog category:', err);
      return ctx.internalServerError(`An error occurred: ${err.message}`);
    }
  }
}));