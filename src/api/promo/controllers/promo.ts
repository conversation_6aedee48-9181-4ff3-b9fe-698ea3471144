// path: src/api/promo/controllers/promo.ts
import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';

export default factories.createCoreController('api::promo.promo', ({ strapi }) => ({
  // Keep default controller actions
  
  // Add custom controller action to update a promo by ID
  async updatePromoById(ctx: Context, next: Next) {
    const { id } = ctx.params;
    
    if (!id) {
      return ctx.badRequest('Promo ID is required in the URL parameters.');
    }
    
    const { data } = ctx.request.body || {};
    
    if (!data || typeof data !== 'object') {
      return ctx.badRequest('Data object is required in the request body.');
    }
    
    try {
      // Check if promo exists
      const existingPromo = await strapi.entityService.findOne('api::promo.promo', id);
      
      if (!existingPromo) {
        return ctx.notFound('Promo not found with the provided ID.');
      }
      
      // Update the promo
      const updatedPromo = await strapi.entityService.update('api::promo.promo', id, {
        data: {
          Title: data.Title,
          promo_description: data.promo_description,
          meta_description: data.meta_description,
          category_fks: data.category_fks,
          product_fks: data.product_fks,
          image_details: data.image_details,
          promo_status: data.promo_status
        },
      });
      
      return ctx.send({
        data: updatedPromo,
        message: 'Promo updated successfully'
      });
    } catch (err: any) {
      console.error('Error updating promo:', err);
      return ctx.internalServerError(`An error occurred: ${err.message}`);
    }
  }
}));