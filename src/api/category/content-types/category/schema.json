{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"Name": {"type": "string"}, "category_description": {"type": "text"}, "meta_description": {"type": "text"}, "category_status": {"type": "string"}, "image_details": {"type": "json"}}}