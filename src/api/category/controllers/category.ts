// path: src/api/category/controllers/category.ts
import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';

export default factories.createCoreController('api::category.category', ({ strapi }) => ({
  // Keep existing find and findOne methods
  async find(ctx: Context) {
    // Call the default find method
    const { data, meta } = await super.find(ctx);
    
    // For each category, count their products
    const enhancedData = await Promise.all(
      data.map(async (category: any) => {
        const productCount = await strapi.db.query('api::product.product').count({
          where: {
            category_fk: category.id
          }
        });
        
        return {
          ...category,
          attributes: {
            ...category.attributes,
            product_count: productCount
          }
        };
      })
    );
    
    return { data: enhancedData, meta };
  },
  
  async findOne(ctx: Context) {
    // Call the default findOne method
    const { data, meta } = await super.findOne(ctx);
    
    // Count products for this category
    const productCount = await strapi.db.query('api::product.product').count({
      where: {
        category_fk: data.id
      }
    });
    
    // Add the count to the response
    return {
      data: {
        ...data,
        attributes: {
          ...data.attributes,
          product_count: productCount
        }
      },
      meta
    };
  },
  
  // Add new updateCategoryById method
  async updateCategoryById(ctx: Context, next: Next) {
    const { id } = ctx.params;
    
    if (!id) {
      return ctx.badRequest('Category ID is required in the URL parameters.');
    }
    
    const { data } = ctx.request.body || {};
    
    if (!data || typeof data !== 'object') {
      return ctx.badRequest('Data object is required in the request body.');
    }
    
    try {
      // Check if category exists
      const existingCategory = await strapi.entityService.findOne('api::category.category', id);
      
      if (!existingCategory) {
        return ctx.notFound('Category not found with the provided ID.');
      }
      
      // Update the category
      const updatedCategory = await strapi.entityService.update('api::category.category', id, {
        data: {
          Name: data.Name,
          category_description: data.category_description,
          meta_description: data.meta_description,
          category_status: data.category_status,
          image_details: data.image_details
        },
      });
      
      // Get product count for this category
      const productCount = await strapi.db.query('api::product.product').count({
        where: {
          category_fk: id
        }
      });
      
      // Add product count to the response
      return ctx.send({
        data: {
          ...updatedCategory,
          product_count: productCount
        },
        message: 'Category updated successfully'
      });
    } catch (err: any) {
      console.error('Error updating category:', err);
      return ctx.internalServerError(`An error occurred: ${err.message}`);
    }
  }
}));