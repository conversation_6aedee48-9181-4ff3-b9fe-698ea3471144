import { factories } from '@strapi/strapi';
import { Context, Next } from 'koa';
import crypto from 'crypto';

export default factories.createCoreController('api::user-registration.user-registration', ({ strapi }) => ({
  // Override the default create method to send an email after registration
  async create(ctx) {
    // Call the default create method
    const response = await super.create(ctx);
    
    // If creation was successful, send notification email
    if (response.data && response.data.id) {
      try {
        // Get the user registration data
        const userRegistrationId = response.data.id;
        const userRegistration = await strapi.db.query('api::user-registration.user-registration').findOne({
          where: { id: userRegistrationId },
        });
        
        if (userRegistration) {
          // Create the login URL
          const loginUrl = process.env.FRONTEND_LOGIN_URL || 'http://localhost:3000/login';

          
          // Send email using SendGrid API
          const emailResponse = await fetch('https://api.sendgrid.com/v3/mail/send', {
            method: 'POST',
            headers: {
              'Authorization': `Bear<PERSON> ${process.env.SENDGRID_API_KEY || '*********************************************************************'}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              personalizations: [{
                to: [{ email: process.env.ADMIN_NOTIFICATION_EMAIL || '<EMAIL>' }],
                dynamic_template_data: {
                  fullName: userRegistration.fullName,
                  first_name: userRegistration.fullName, // Using fullName as first_name for the email greeting
                  loginUrl: loginUrl,
                  login_url: loginUrl,
                  button_url: loginUrl // Adding another variable name that might be used in the template
                }
              }],
              from: {
                email: '<EMAIL>',
                name: 'Salus'
              },
              template_id: 'd-9856e851e6754192baa3a1df2a3e69db',
              tracking_settings: {
                click_tracking: {
                  enable: false
                }
              }
            })
          });
          
          if (!emailResponse.ok) {
            const errorData = await emailResponse.json();
            console.error('SendGrid email error:', errorData);
            // We continue even if email fails
          } else {
            console.log('Registration notification email sent successfully');
          }
        }
      } catch (emailError) {
        console.error('Error sending registration notification email:', emailError);
        // Continue with the registration process even if email sending fails
      }
    }
    
    return response;
  },

  async processRegistrationByDocumentId(ctx: Context, next: Next) {
    const { documentId } = ctx.query;
    console.log('Request Body:', ctx.request.body); // Added for debugging

    if (!documentId) {
      return ctx.badRequest('documentId parameter is required in the query.');
    }

    const { approvedByAdminName, mode } = ctx.request.body?.data || {};

    if (!approvedByAdminName || typeof approvedByAdminName !== 'string' || approvedByAdminName.trim() === '') {
      return ctx.badRequest('approvedByAdminName is required in the request body (within the "data" object) and must be a non-empty string.');
    }

    if (!mode || typeof mode !== 'string' || !['approve', 'decline', 'remove'].includes(mode)) {
      return ctx.badRequest('mode is required in the request body (within the "data" object) and must be one of: approve, decline, or remove.');
    }

    try {
      // First, find the user registration record by documentId
      const userRegistration = await strapi.db.query('api::user-registration.user-registration').findOne({
        where: { documentId: documentId as string },
      });
      console.log('Fetched userRegistration:', userRegistration);
      if (userRegistration) {
         console.log('Fetched documentId:', userRegistration.documentId);}
      if (!userRegistration) {
        return ctx.notFound('No user registration found with the provided documentId.');
      }

      let updateData: any = {
        approved_by_admin_name: approvedByAdminName.trim(),
      };
      
      let responseMessage = '';
      let resetPasswordToken = '';
      
      switch (mode) {
        case 'approve':
          // Generate a random unique token
          const userRole = await strapi.db.query('plugin::users-permissions.role').findOne({
            where: { name: 'User' }
          });
          resetPasswordToken = crypto.randomBytes(32).toString('hex');
          updateData.reg_status = 'Approved';
          updateData.user_status = false;
          responseMessage = `Successfully approved registration by ${approvedByAdminName.trim()}.`;
          
          // Create or update a user in the users table
          try {
            // Check if the user with this email already exists
            const existingUser = await strapi.db.query('plugin::users-permissions.user').findOne({
              where: { email: userRegistration.email },
            });

            let userId;
            if (!existingUser) {
              // Create the user if it doesn't exist
              const newUser = await strapi.db.query('plugin::users-permissions.user').create({
                data: {
                  username: userRegistration.fullName,
                  email: userRegistration.email,
                  user_status: false,
                  provider: 'local',
                  confirmed: false,
                  blocked: false,
                  resetPasswordToken: resetPasswordToken,
                  role: userRole
                }
              });
              userId = newUser.id;
            } else {
              // Update the existing user
              await strapi.db.query('plugin::users-permissions.user').update({
                where: { id: existingUser.id },
                data: {
                  confirmed: false,
                  resetPasswordToken: resetPasswordToken,
                  role: userRole
                }
              });
              userId = existingUser.id;
            }

            // --- NEW CODE: Set the user_registration_fk field ---
            if (userId && userRegistration.documentId) {
              await strapi.db.query('plugin::users-permissions.user').update({
                where: { id: userId },
                data: {
                  user_registration_fk: userRegistration.id
                }
              });
            }
            // --- END NEW CODE ---

            // Send approval email via SendGrid
            try {
              // Create a simple URL string without any special characters
              const resetUrl = process.env.FRONTEND_RESET_PASSWORD_URL || 'http://localhost:3000/set-password';
              const tokenParam = resetPasswordToken;
              
              // Create a URL that's safe for email templates
              const safeResetUrl = resetUrl + '?token=' + tokenParam;
              
              // Send email using SendGrid API
              const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${process.env.SENDGRID_API_KEY || '*********************************************************************'}`,
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  personalizations: [{
                    to: [{ email: userRegistration.email }],
                    dynamic_template_data: {
                      fullName: userRegistration.fullName,
                      first_name: userRegistration.fullName, // Using fullName as first_name for the email greeting
                      resetUrl: safeResetUrl,
                      reset_url: safeResetUrl,
                      button_url: safeResetUrl, // Adding another variable name that might be used in the template
                      // Add any other variables your template needs
                    }
                  }],
                  from: {
                    email: '<EMAIL>',
                    name: 'Salus'
                  },
                  template_id: 'd-ef02325330944b6aa4f724399a6c0eb3',
                  tracking_settings: {
                    click_tracking: {
                      enable: false
                    }
                  }
                })
              });
              
              if (!response.ok) {
                const errorData = await response.json();
                console.error('SendGrid email error:', errorData);
                // We continue even if email fails
              } else {
                console.log('Approval email sent successfully');
              }
            } catch (emailError) {
              console.error('Error sending approval email:', emailError);
              // Continue with the approval process even if email sending fails
            }
            
          } catch (userCreateError) {
            console.error('Error creating/updating user:', userCreateError);
            // Continue with the approval process even if user creation fails
          }
          
          break;
        
        case 'decline':
          updateData.reg_status = 'Declined';
          responseMessage = `Successfully declined registration by ${approvedByAdminName.trim()}.`;
          break;
          
        case 'remove':
          updateData.reg_status = 'Removed';
          responseMessage = `Successfully removed registration by ${approvedByAdminName.trim()}.`;
          
          // Find and update the corresponding user in the users table
          try {
            const existingUser = await strapi.db.query('plugin::users-permissions.user').findOne({
              where: { email: userRegistration.email },
            });
            
            if (existingUser) {
              // Update the user's confirmed status to false
              await strapi.db.query('plugin::users-permissions.user').update({
                where: { id: existingUser.id },
                data: {
                  confirmed: false
                }
              });
            }
          } catch (userUpdateError) {
            console.error('Error updating user in remove mode:', userUpdateError);
            // Continue with the removal process even if user update fails
          }
          
          break;
      }
      
      const updatedEntries: { count: number } = await strapi.db.query('api::user-registration.user-registration').updateMany({
        where: { documentId: documentId as string },
        data: updateData,
      });

      if (updatedEntries.count > 0) {
        const response: any = { 
          message: responseMessage
        };
        
        // Only include resetPasswordToken in the response for approve mode
        if (mode === 'approve') {
          response.resetPasswordToken = resetPasswordToken;
        }
        
        return ctx.send(response);
      } else {
        return ctx.notFound('No user registrations found with the provided documentId.');
      }
    } catch (err: any) {
      return ctx.internalServerError(err);
    }
  },
  
  // Keep the old method for backward compatibility but make it call the new method
  async approveByDocumentId(ctx: Context, next: Next) {
    // If mode is not provided, default to 'approve'
    if (ctx.request.body?.data && !ctx.request.body.data.mode) {
      ctx.request.body.data.mode = 'approve';
    }
    
    return this.processRegistrationByDocumentId(ctx, next);
  },

  // Handle forgot password requests
  async forgotPassword(ctx: Context, next: Next) {
    const { email } = ctx.request.body?.data || {};

    if (!email || typeof email !== 'string' || email.trim() === '') {
      return ctx.badRequest('Email is required in the request body (within the "data" object) and must be a non-empty string.');
    }

    try {
      // Find the user with the provided email
      const user = await strapi.db.query('plugin::users-permissions.user').findOne({
        where: { email: email.trim().toLowerCase() }
      });

      if (!user) {
        // For security reasons, don't reveal that the email doesn't exist
        // Instead, return a success message as if the email was sent
        return ctx.send({ 
          message: 'If a user with this email exists, a password reset link has been sent.' 
        });
      }

      // Generate a random unique token
      const resetPasswordToken = crypto.randomBytes(32).toString('hex');

      // Update the user with the new reset token
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: {
          resetPasswordToken: resetPasswordToken
        }
      });

      // Create the reset password URL
      const resetUrl = process.env.FRONTEND_RESET_PASSWORD_URL || 'http://localhost:3000/set-password';
      const tokenParam = resetPasswordToken;
      const safeResetUrl = resetUrl + '?token=' + tokenParam;

      // Send email using SendGrid API
      try {
        const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.SENDGRID_API_KEY || '*********************************************************************'}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            personalizations: [{
              to: [{ email: user.email }],
              dynamic_template_data: {
                fullName: user.username, // Using username as fullName
                first_name: user.username, // Using username as first_name for the email greeting
                resetUrl: safeResetUrl,
                reset_url: safeResetUrl,
                button_url: safeResetUrl, // Using the variable name that works with the template
              }
            }],
            from: {
              email: '<EMAIL>',
              name: 'Salus'
            },
            template_id: 'd-88adfa22d66946e7b3ce51717dd15da0', // Forgot password template ID
            tracking_settings: {
              click_tracking: {
                enable: false
              }
            }
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('SendGrid email error:', errorData);
          // Continue even if email fails
        } else {
          console.log('Forgot password email sent successfully');
        }
      } catch (emailError) {
        console.error('Error sending forgot password email:', emailError);
        // Continue even if email sending fails
      }

      // Return success message
      return ctx.send({ 
        message: 'If a user with this email exists, a password reset link has been sent.' 
      });
    } catch (err: any) {
      console.error('Forgot password error:', err);
      return ctx.internalServerError('An error occurred while processing your request.');
    }
  }
}));