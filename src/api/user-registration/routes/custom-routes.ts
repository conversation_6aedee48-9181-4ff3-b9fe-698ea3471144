/**
 * Custom routes for user-registration
 */
export default {
    routes: [
      {
        method: 'PUT',
        path: '/user-registrations/approve-by-document-id',
        handler: 'api::user-registration.user-registration.approveByDocumentId',
        config: {
          policies: [],
        },
      },
      {
        method: 'POST',
        path: '/user-registrations/forgot-password',
        handler: 'api::user-registration.user-registration.forgotPassword',
        config: {
          policies: [],
        },
      },
    ],
  };