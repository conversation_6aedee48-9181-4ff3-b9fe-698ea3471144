{"kind": "collectionType", "collectionName": "user_registrations", "info": {"singularName": "user-registration", "pluralName": "user-registrations", "displayName": "user-registration", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"fullName": {"type": "string"}, "email": {"type": "email", "unique": true}, "mobileNumber": {"type": "string"}, "businessName": {"type": "string"}, "businessAddress": {"type": "string"}, "businessPhone": {"type": "string"}, "abn": {"type": "string"}, "aphraNumber": {"type": "string"}, "title": {"type": "string"}, "speciality": {"type": "string"}, "reg_status": {"type": "string"}, "approved_by_admin_name": {"type": "string"}}}