import { mergeConfig, type UserConfig } from 'vite';

export default (config: UserConfig) => {
  // Merge your existing configuration with the new server settings
  return mergeConfig(config, {
    resolve: {
      alias: {
        '@': '/src',
      },
    },
    server: {
      allowedHosts: [
        'localhost',
        '127.0.0.1',
        'salusstaging.findwhyclients.com',
        '************'
      ],
    },
  });
};