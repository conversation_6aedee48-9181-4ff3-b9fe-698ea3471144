export default [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::cors',
    config: {
      // Explicitly list all origins that should be allowed
      origin: ['https://salus-frontend.findwhyclients.com', 'https://salusaus.com.au','https://www.salusaus.com.au', 'http://*************:3000', 'http://localhost:3000'],
      
      // Comprehensive list of methods
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
      
      // All required headers
      headers: ['Content-Type', 'Authorization', 'Origin', 'Accept'],
      
      // Enable credential support
      credentials: true,
      
      // Keep headers on error responses
      keepHeaderOnError: true,
      
      // Ensure CORS is enabled
      enabled: true
    },
  },
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'https:', 'ws:', 'wss:'],
          'img-src': ["'self'", 'data:', 'blob:', 'https://salusstaging.findwhyclients.com'],
          'media-src': ["'self'", 'data:', 'blob:'],
          'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          'frame-src': ["'self'"],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
]