interface XeroConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string;
}

export default ({ env }): XeroConfig => {
  const clientId = env('XERO_CLIENT_ID');
  const clientSecret = env('XERO_CLIENT_SECRET');
  const redirectUri = env('XERO_REDIRECT_URI');

  console.log('Loaded Xero config:', {
    clientId: process.env.XERO_CLIENT_ID,
    clientSecret: process.env.XERO_CLIENT_SECRET,
    redirectUri: process.env.XERO_REDIRECT_URI,
  });

  if (!clientId || !clientSecret || !redirectUri) {
    console.error('Missing Xero configuration:', {
      hasClientId: <PERSON><PERSON><PERSON>(clientId),
      hasClientSecret: <PERSON><PERSON>an(clientSecret),
      hasRedirectUri: <PERSON><PERSON>an(redirectUri)
    });
  }

  return {
    clientId,
    clientSecret,
    redirectUri,
    scopes: 'accounting.transactions accounting.settings offline_access',
  };
};
