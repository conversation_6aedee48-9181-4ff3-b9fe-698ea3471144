export default ({ env }) => ({
  auth: {
    secret: env('ADMIN_JWT_SECRET'),
  },
  apiToken: {
    salt: env('API_TOKEN_SALT'),
  },
  transfer: {
    token: {
      salt: env('TRANSFER_TOKEN_SALT'),
    },
  },
  flags: {
    nps: env.bool('FLAG_NPS', true),
    promoteEE: env.bool('FLAG_PROMOTE_EE', true),
  },
  url: env('PUBLIC_ADMIN_URL', '/admin'),
  host: env('HOST', '0.0.0.0'), // Allow all hosts
  port: env.int('ADMIN_PORT', 8000),
  serveAdminPanel: true,
  autoOpen: false,
  watchIgnoreFiles: [
    '**/node_modules/**',
    '**/build/**',
  ],
});